use anyhow::Result;
use serde::{Deserialize, Serialize};
use crate::core::capture::{Capture<PERSON>rame, AudioSample};

/// Video encoder configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VideoEncoderConfig {
    pub width: u32,
    pub height: u32,
    pub framerate: u32,
    pub bitrate: u32,
    pub keyframe_interval: u32,
    pub codec: VideoCodec,
    pub hardware_acceleration: bool,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum VideoCodec {
    H264,
    H265,
    VP8,
    VP9,
    AV1,
}

/// Audio encoder configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AudioEncoderConfig {
    pub sample_rate: u32,
    pub channels: u16,
    pub bitrate: u32,
    pub codec: AudioCodec,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AudioCodec {
    AAC,
    Opus,
    MP3,
}

/// Encoded video frame
#[derive(Debug, <PERSON><PERSON>)]
pub struct EncodedVideoFrame {
    pub data: Vec<u8>,
    pub timestamp: u64,
    pub is_keyframe: bool,
    pub frame_type: FrameType,
}

#[derive(Debug, <PERSON>lone)]
pub enum FrameType {
    I, // Intra frame (keyframe)
    P, // Predicted frame
    B, // Bidirectional frame
}

/// Encoded audio frame
#[derive(Debug, Clone)]
pub struct EncodedAudioFrame {
    pub data: Vec<u8>,
    pub timestamp: u64,
    pub sample_count: u32,
}

/// Video encoder trait
#[async_trait::async_trait]
pub trait VideoEncoder: Send + Sync {
    async fn configure(&mut self, config: VideoEncoderConfig) -> Result<()>;
    async fn encode_frame(&mut self, frame: &CaptureFrame) -> Result<Option<EncodedVideoFrame>>;
    async fn flush(&mut self) -> Result<Vec<EncodedVideoFrame>>;
    fn get_config(&self) -> &VideoEncoderConfig;
    fn update_bitrate(&mut self, bitrate: u32) -> Result<()>;
    fn request_keyframe(&mut self) -> Result<()>;
}

/// Audio encoder trait
#[async_trait::async_trait]
pub trait AudioEncoder: Send + Sync {
    async fn configure(&mut self, config: AudioEncoderConfig) -> Result<()>;
    async fn encode_sample(&mut self, sample: &AudioSample) -> Result<Option<EncodedAudioFrame>>;
    async fn flush(&mut self) -> Result<Vec<EncodedAudioFrame>>;
    fn get_config(&self) -> &AudioEncoderConfig;
}

/// Encoding manager that handles both video and audio encoding
pub struct EncodingManager {
    video_encoder: Option<Box<dyn VideoEncoder>>,
    audio_encoder: Option<Box<dyn AudioEncoder>>,
    video_config: Option<VideoEncoderConfig>,
    audio_config: Option<AudioEncoderConfig>,
}

impl EncodingManager {
    pub fn new() -> Self {
        Self {
            video_encoder: None,
            audio_encoder: None,
            video_config: None,
            audio_config: None,
        }
    }

    pub fn set_video_encoder(&mut self, encoder: Box<dyn VideoEncoder>) {
        self.video_encoder = Some(encoder);
    }

    pub fn set_audio_encoder(&mut self, encoder: Box<dyn AudioEncoder>) {
        self.audio_encoder = Some(encoder);
    }

    pub async fn configure_video(&mut self, config: VideoEncoderConfig) -> Result<()> {
        if let Some(encoder) = &mut self.video_encoder {
            encoder.configure(config.clone()).await?;
            self.video_config = Some(config);
        }
        Ok(())
    }

    pub async fn configure_audio(&mut self, config: AudioEncoderConfig) -> Result<()> {
        if let Some(encoder) = &mut self.audio_encoder {
            encoder.configure(config.clone()).await?;
            self.audio_config = Some(config);
        }
        Ok(())
    }

    pub async fn encode_video_frame(&mut self, frame: &CaptureFrame) -> Result<Option<EncodedVideoFrame>> {
        if let Some(encoder) = &mut self.video_encoder {
            encoder.encode_frame(frame).await
        } else {
            Ok(None)
        }
    }

    pub async fn encode_audio_sample(&mut self, sample: &AudioSample) -> Result<Option<EncodedAudioFrame>> {
        if let Some(encoder) = &mut self.audio_encoder {
            encoder.encode_sample(sample).await
        } else {
            Ok(None)
        }
    }

    pub fn update_video_bitrate(&mut self, bitrate: u32) -> Result<()> {
        if let Some(encoder) = &mut self.video_encoder {
            encoder.update_bitrate(bitrate)?;
            if let Some(config) = &mut self.video_config {
                config.bitrate = bitrate;
            }
        }
        Ok(())
    }

    pub fn request_keyframe(&mut self) -> Result<()> {
        if let Some(encoder) = &mut self.video_encoder {
            encoder.request_keyframe()?;
        }
        Ok(())
    }

    pub async fn flush_video(&mut self) -> Result<Vec<EncodedVideoFrame>> {
        if let Some(encoder) = &mut self.video_encoder {
            encoder.flush().await
        } else {
            Ok(vec![])
        }
    }

    pub async fn flush_audio(&mut self) -> Result<Vec<EncodedAudioFrame>> {
        if let Some(encoder) = &mut self.audio_encoder {
            encoder.flush().await
        } else {
            Ok(vec![])
        }
    }

    pub fn get_video_config(&self) -> Option<&VideoEncoderConfig> {
        self.video_config.as_ref()
    }

    pub fn get_audio_config(&self) -> Option<&AudioEncoderConfig> {
        self.audio_config.as_ref()
    }
}

/// Adaptive bitrate controller
pub struct BitrateController {
    current_bitrate: u32,
    target_bitrate: u32,
    min_bitrate: u32,
    max_bitrate: u32,
    network_state: NetworkState,
}

#[derive(Debug, Clone)]
pub enum NetworkState {
    Underuse,
    Normal,
    Overuse,
}

impl BitrateController {
    pub fn new(initial_bitrate: u32, min_bitrate: u32, max_bitrate: u32) -> Self {
        Self {
            current_bitrate: initial_bitrate,
            target_bitrate: initial_bitrate,
            min_bitrate,
            max_bitrate,
            network_state: NetworkState::Normal,
        }
    }

    pub fn update_network_stats(&mut self, rtt: u32, packet_loss: f32, bandwidth_estimate: Option<u32>) {
        // Implement AIMD (Additive Increase, Multiplicative Decrease) algorithm
        self.network_state = self.analyze_network_state(rtt, packet_loss);

        match self.network_state {
            NetworkState::Overuse => {
                // Multiplicative decrease
                self.target_bitrate = (self.current_bitrate as f32 * 0.85) as u32;
            }
            NetworkState::Underuse => {
                // Additive increase
                let increment = (self.max_bitrate - self.current_bitrate) / 20; // 5% increase
                self.target_bitrate = self.current_bitrate + increment.max(50000); // At least 50kbps increase
            }
            NetworkState::Normal => {
                // Keep current bitrate
            }
        }

        // Clamp to min/max bounds
        self.target_bitrate = self.target_bitrate.clamp(self.min_bitrate, self.max_bitrate);
        self.current_bitrate = self.target_bitrate;
    }

    fn analyze_network_state(&self, rtt: u32, packet_loss: f32) -> NetworkState {
        if packet_loss > 0.10 || rtt > 200 {
            NetworkState::Overuse
        } else if packet_loss < 0.02 && rtt < 50 {
            NetworkState::Underuse
        } else {
            NetworkState::Normal
        }
    }

    pub fn get_current_bitrate(&self) -> u32 {
        self.current_bitrate
    }

    pub fn get_target_bitrate(&self) -> u32 {
        self.target_bitrate
    }
}
