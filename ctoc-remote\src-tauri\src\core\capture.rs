use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::mpsc;

/// Screen capture frame data
#[derive(Debug, Clone)]
pub struct CaptureFrame {
    pub width: u32,
    pub height: u32,
    pub data: Vec<u8>,
    pub format: PixelFormat,
    pub timestamp: u64,
    pub dirty_rects: Vec<DirtyRect>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum PixelFormat {
    BGRA,
    RGBA,
    RGB,
    YUV420,
}

#[derive(Debug, Clone)]
pub struct DirtyRect {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

/// Audio capture sample data
#[derive(Debug, <PERSON>lone)]
pub struct AudioSample {
    pub data: Vec<f32>,
    pub sample_rate: u32,
    pub channels: u16,
    pub timestamp: u64,
}

/// Screen capture trait for platform-specific implementations
#[async_trait::async_trait]
pub trait ScreenCapture: Send + Sync {
    async fn start_capture(&mut self) -> Result<()>;
    async fn stop_capture(&mut self) -> Result<()>;
    async fn get_next_frame(&mut self) -> Result<Option<CaptureFrame>>;
    fn get_screen_info(&self) -> ScreenInfo;
}

/// Audio capture trait for platform-specific implementations
#[async_trait::async_trait]
pub trait AudioCapture: Send + Sync {
    async fn start_capture(&mut self) -> Result<()>;
    async fn stop_capture(&mut self) -> Result<()>;
    async fn get_next_sample(&mut self) -> Result<Option<AudioSample>>;
    fn get_audio_info(&self) -> AudioInfo;
}

#[derive(Debug, Clone)]
pub struct ScreenInfo {
    pub width: u32,
    pub height: u32,
    pub refresh_rate: u32,
    pub scale_factor: f32,
}

#[derive(Debug, Clone)]
pub struct AudioInfo {
    pub sample_rate: u32,
    pub channels: u16,
    pub bit_depth: u16,
}

/// Capture manager that coordinates screen and audio capture
pub struct CaptureManager {
    screen_capture: Option<Box<dyn ScreenCapture>>,
    audio_capture: Option<Box<dyn AudioCapture>>,
    frame_sender: Option<mpsc::UnboundedSender<CaptureFrame>>,
    audio_sender: Option<mpsc::UnboundedSender<AudioSample>>,
}

impl CaptureManager {
    pub fn new() -> Self {
        Self {
            screen_capture: None,
            audio_capture: None,
            frame_sender: None,
            audio_sender: None,
        }
    }

    pub fn set_screen_capture(&mut self, capture: Box<dyn ScreenCapture>) {
        self.screen_capture = Some(capture);
    }

    pub fn set_audio_capture(&mut self, capture: Box<dyn AudioCapture>) {
        self.audio_capture = Some(capture);
    }

    pub fn set_frame_sender(&mut self, sender: mpsc::UnboundedSender<CaptureFrame>) {
        self.frame_sender = Some(sender);
    }

    pub fn set_audio_sender(&mut self, sender: mpsc::UnboundedSender<AudioSample>) {
        self.audio_sender = Some(sender);
    }

    pub async fn start_capture(&mut self) -> Result<()> {
        if let Some(screen_capture) = &mut self.screen_capture {
            screen_capture.start_capture().await?;
        }

        if let Some(audio_capture) = &mut self.audio_capture {
            audio_capture.start_capture().await?;
        }

        // Start capture loops
        self.start_screen_capture_loop().await?;
        self.start_audio_capture_loop().await?;

        Ok(())
    }

    pub async fn stop_capture(&mut self) -> Result<()> {
        if let Some(screen_capture) = &mut self.screen_capture {
            screen_capture.stop_capture().await?;
        }

        if let Some(audio_capture) = &mut self.audio_capture {
            audio_capture.stop_capture().await?;
        }

        Ok(())
    }

    async fn start_screen_capture_loop(&mut self) -> Result<()> {
        // TODO: Implement screen capture loop
        Ok(())
    }

    async fn start_audio_capture_loop(&mut self) -> Result<()> {
        // TODO: Implement audio capture loop
        Ok(())
    }

    pub fn get_screen_info(&self) -> Option<ScreenInfo> {
        self.screen_capture.as_ref().map(|c| c.get_screen_info())
    }

    pub fn get_audio_info(&self) -> Option<AudioInfo> {
        self.audio_capture.as_ref().map(|c| c.get_audio_info())
    }
}

// Platform-specific implementations will be added in separate modules
#[cfg(target_os = "windows")]
pub mod windows;

#[cfg(target_os = "macos")]
pub mod macos;

#[cfg(target_os = "linux")]
pub mod linux;
