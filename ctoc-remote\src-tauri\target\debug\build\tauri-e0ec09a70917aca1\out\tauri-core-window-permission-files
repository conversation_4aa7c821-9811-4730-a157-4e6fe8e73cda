["\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\available_monitors.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\center.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\close.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\create.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\current_monitor.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\cursor_position.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\destroy.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\get_all_windows.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\hide.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\inner_position.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\inner_size.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\internal_toggle_maximize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_always_on_top.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_closable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_decorated.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_focused.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_fullscreen.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_maximizable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_maximized.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_minimizable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_minimized.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_resizable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\is_visible.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\maximize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\minimize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\monitor_from_point.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\outer_position.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\outer_size.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\primary_monitor.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\request_user_attention.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\scale_factor.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_bottom.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_top.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_background_color.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_badge_count.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_badge_label.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_closable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_content_protected.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_grab.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_icon.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_position.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_visible.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_decorations.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_effects.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_focus.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_fullscreen.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_ignore_cursor_events.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_max_size.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_maximizable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_min_size.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_minimizable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_overlay_icon.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_position.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_progress_bar.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_resizable.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_shadow.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_size.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_size_constraints.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_skip_taskbar.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_theme.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_title.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_title_bar_style.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\set_visible_on_all_workspaces.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\show.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\start_dragging.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\start_resize_dragging.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\theme.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\title.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\toggle_maximize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\unmaximize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\commands\\unminimize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\window\\autogenerated\\default.toml"]