use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use anyhow::Result;

/// Application configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppConfig {
    pub device: DeviceConfig,
    pub network: NetworkConfig,
    pub video: VideoConfig,
    pub audio: AudioConfig,
    pub security: SecurityConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DeviceConfig {
    pub device_name: String,
    pub auto_start: bool,
    pub minimize_to_tray: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub signaling_server: String,
    pub stun_servers: Vec<String>,
    pub turn_servers: Vec<TurnServer>,
    pub connection_timeout: u32,
    pub heartbeat_interval: u32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TurnServer {
    pub url: String,
    pub username: String,
    pub credential: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VideoConfig {
    pub default_width: u32,
    pub default_height: u32,
    pub default_framerate: u32,
    pub default_bitrate: u32,
    pub min_bitrate: u32,
    pub max_bitrate: u32,
    pub hardware_acceleration: bool,
    pub codec_preference: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioConfig {
    pub enabled: bool,
    pub sample_rate: u32,
    pub channels: u16,
    pub bitrate: u32,
    pub codec_preference: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub otp_expiry_seconds: u32,
    pub max_connection_attempts: u32,
    pub require_confirmation: bool,
    pub auto_accept_trusted_devices: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            device: DeviceConfig {
                device_name: "My Device".to_string(),
                auto_start: false,
                minimize_to_tray: true,
            },
            network: NetworkConfig {
                signaling_server: "wss://localhost:8080".to_string(),
                stun_servers: vec![
                    "stun:stun.l.google.com:19302".to_string(),
                    "stun:stun1.l.google.com:19302".to_string(),
                ],
                turn_servers: vec![],
                connection_timeout: 30,
                heartbeat_interval: 30,
            },
            video: VideoConfig {
                default_width: 1920,
                default_height: 1080,
                default_framerate: 30,
                default_bitrate: 2000000, // 2 Mbps
                min_bitrate: 500000,       // 500 kbps
                max_bitrate: 10000000,     // 10 Mbps
                hardware_acceleration: true,
                codec_preference: vec!["h264".to_string(), "vp8".to_string()],
            },
            audio: AudioConfig {
                enabled: true,
                sample_rate: 48000,
                channels: 2,
                bitrate: 128000, // 128 kbps
                codec_preference: vec!["opus".to_string(), "aac".to_string()],
            },
            security: SecurityConfig {
                otp_expiry_seconds: 300, // 5 minutes
                max_connection_attempts: 3,
                require_confirmation: true,
                auto_accept_trusted_devices: false,
            },
        }
    }
}

/// Configuration manager
pub struct ConfigManager {
    config: AppConfig,
    config_path: PathBuf,
}

impl ConfigManager {
    pub fn new() -> Result<Self> {
        let config_path = Self::get_config_path()?;
        let config = Self::load_config(&config_path)?;
        
        Ok(Self {
            config,
            config_path,
        })
    }

    fn get_config_path() -> Result<PathBuf> {
        let mut path = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Failed to get config directory"))?;
        
        path.push("ctoc-remote");
        std::fs::create_dir_all(&path)?;
        path.push("config.json");
        
        Ok(path)
    }

    fn load_config(path: &PathBuf) -> Result<AppConfig> {
        if path.exists() {
            let content = std::fs::read_to_string(path)?;
            let config: AppConfig = serde_json::from_str(&content)?;
            Ok(config)
        } else {
            let config = AppConfig::default();
            Self::save_config_to_file(&config, path)?;
            Ok(config)
        }
    }

    fn save_config_to_file(config: &AppConfig, path: &PathBuf) -> Result<()> {
        let content = serde_json::to_string_pretty(config)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    pub fn get_config(&self) -> &AppConfig {
        &self.config
    }

    pub fn get_config_mut(&mut self) -> &mut AppConfig {
        &mut self.config
    }

    pub fn save_config(&self) -> Result<()> {
        Self::save_config_to_file(&self.config, &self.config_path)
    }

    pub fn update_device_config(&mut self, device_config: DeviceConfig) -> Result<()> {
        self.config.device = device_config;
        self.save_config()
    }

    pub fn update_network_config(&mut self, network_config: NetworkConfig) -> Result<()> {
        self.config.network = network_config;
        self.save_config()
    }

    pub fn update_video_config(&mut self, video_config: VideoConfig) -> Result<()> {
        self.config.video = video_config;
        self.save_config()
    }

    pub fn update_audio_config(&mut self, audio_config: AudioConfig) -> Result<()> {
        self.config.audio = audio_config;
        self.save_config()
    }

    pub fn update_security_config(&mut self, security_config: SecurityConfig) -> Result<()> {
        self.config.security = security_config;
        self.save_config()
    }

    pub fn reset_to_default(&mut self) -> Result<()> {
        self.config = AppConfig::default();
        self.save_config()
    }
}
