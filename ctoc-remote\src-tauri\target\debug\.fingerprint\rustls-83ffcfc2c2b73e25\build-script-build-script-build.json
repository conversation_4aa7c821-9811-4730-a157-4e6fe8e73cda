{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 5408242616063297496, "profile": 16865373277404624699, "path": 4516336869252030033, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-83ffcfc2c2b73e25\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}