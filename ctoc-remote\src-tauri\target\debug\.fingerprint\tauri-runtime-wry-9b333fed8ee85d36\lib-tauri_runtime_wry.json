{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 3438318040917731529, "deps": [[376837177317575824, "softbuffer", false, 2563869346384267298], [2013030631243296465, "webview2_com", false, 3020859597941808250], [2671782512663819132, "tauri_utils", false, 12611128184557746340], [3150220818285335163, "url", false, 15436067219720112665], [3722963349756955755, "once_cell", false, 473892535712684218], [4143744114649553716, "raw_window_handle", false, 2349762867116203253], [5986029879202738730, "log", false, 13613251818465215128], [6089812615193535349, "tauri_runtime", false, 11684447462034376327], [8826339825490770380, "tao", false, 10902976126139346049], [9010263965687315507, "http", false, 11900686201987544601], [9141053277961803901, "wry", false, 4666205531649411556], [11599800339996261026, "build_script_build", false, 1296787261632281246], [14585479307175734061, "windows", false, 12606393980976928651]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-9b333fed8ee85d36\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}