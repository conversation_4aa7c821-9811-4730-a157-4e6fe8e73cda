["\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\menu\\autogenerated\\default.toml"]