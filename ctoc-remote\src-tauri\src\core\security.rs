use serde::{Deserialize, Serialize};
use rand::Rng;
use sha2::{Sha256, Digest};
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, KeyInit}};
use anyhow::{Result, anyhow};
use std::time::{SystemTime, UNIX_EPOCH};

/// One-Time Password (OTP) for device pairing
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Otp {
    pub code: String,
    pub expires_at: u64,
    pub device_id: String,
}

/// Security manager for handling encryption and authentication
pub struct SecurityManager {
    device_secret: Vec<u8>,
}

impl SecurityManager {
    pub fn new() -> Result<Self> {
        let device_secret = Self::generate_device_secret();
        Ok(Self { device_secret })
    }

    /// Generate a unique device secret for this instance
    fn generate_device_secret() -> Vec<u8> {
        let mut rng = rand::thread_rng();
        (0..32).map(|_| rng.gen::<u8>()).collect()
    }

    /// Generate a 6-digit OTP for device pairing
    pub fn generate_otp(&self, device_id: &str) -> Result<Otp> {
        let mut rng = rand::thread_rng();
        let code = format!("{:06}", rng.gen_range(100000..999999));
        
        let expires_at = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs() + 300; // 5 minutes expiry

        Ok(Otp {
            code,
            expires_at,
            device_id: device_id.to_string(),
        })
    }

    /// Verify an OTP
    pub fn verify_otp(&self, otp: &Otp, provided_code: &str) -> Result<bool> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();

        if current_time > otp.expires_at {
            return Ok(false); // OTP expired
        }

        Ok(otp.code == provided_code)
    }

    /// Generate a session key for WebRTC DTLS
    pub fn generate_session_key(&self) -> Vec<u8> {
        let mut rng = rand::thread_rng();
        (0..32).map(|_| rng.gen::<u8>()).collect()
    }

    /// Encrypt data using AES-256-GCM
    pub fn encrypt_data(&self, data: &[u8], key: &[u8]) -> Result<Vec<u8>> {
        if key.len() != 32 {
            return Err(anyhow!("Key must be 32 bytes for AES-256"));
        }

        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
        let mut rng = rand::thread_rng();
        let nonce_bytes: [u8; 12] = rng.gen();
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = cipher.encrypt(nonce, data)
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // Prepend nonce to ciphertext
        let mut result = nonce_bytes.to_vec();
        result.extend_from_slice(&ciphertext);
        Ok(result)
    }

    /// Decrypt data using AES-256-GCM
    pub fn decrypt_data(&self, encrypted_data: &[u8], key: &[u8]) -> Result<Vec<u8>> {
        if key.len() != 32 {
            return Err(anyhow!("Key must be 32 bytes for AES-256"));
        }

        if encrypted_data.len() < 12 {
            return Err(anyhow!("Encrypted data too short"));
        }

        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
        let nonce = Nonce::from_slice(&encrypted_data[..12]);
        let ciphertext = &encrypted_data[12..];

        let plaintext = cipher.decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        Ok(plaintext)
    }

    /// Generate a hash of the device fingerprint
    pub fn generate_device_fingerprint(&self) -> String {
        let mut hasher = Sha256::new();
        hasher.update(&self.device_secret);
        hasher.update(std::env::consts::OS.as_bytes());
        hasher.update(std::env::consts::ARCH.as_bytes());
        
        let result = hasher.finalize();
        base64::encode(result)
    }

    /// Validate a device fingerprint
    pub fn validate_fingerprint(&self, fingerprint: &str) -> bool {
        let expected = self.generate_device_fingerprint();
        expected == fingerprint
    }
}

impl Default for SecurityManager {
    fn default() -> Self {
        Self::new().expect("Failed to create SecurityManager")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_otp_generation() {
        let security = SecurityManager::new().unwrap();
        let otp = security.generate_otp("test-device").unwrap();
        
        assert_eq!(otp.code.len(), 6);
        assert!(otp.code.chars().all(|c| c.is_ascii_digit()));
        assert_eq!(otp.device_id, "test-device");
    }

    #[test]
    fn test_encryption_decryption() {
        let security = SecurityManager::new().unwrap();
        let key = security.generate_session_key();
        let data = b"Hello, World!";

        let encrypted = security.encrypt_data(data, &key).unwrap();
        let decrypted = security.decrypt_data(&encrypted, &key).unwrap();

        assert_eq!(data, decrypted.as_slice());
    }
}
