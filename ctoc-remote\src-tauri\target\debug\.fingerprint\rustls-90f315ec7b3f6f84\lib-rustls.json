{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5788819337146887687, "path": 1501860850565253547, "deps": [[2883436298747778685, "pki_types", false, 3108325932874360495], [3722963349756955755, "once_cell", false, 473892535712684218], [5491919304041016563, "ring", false, 15822655434518384680], [6528079939221783635, "zeroize", false, 13582192474966568929], [16400140949089969347, "build_script_build", false, 14032393867579697545], [17003143334332120809, "subtle", false, 14868275846898199188], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 8574857203717357357]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-90f315ec7b3f6f84\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}