{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 2241668132362809309, "path": 10851868509090417691, "deps": [[2883436298747778685, "pki_types", false, 3108325932874360495], [5491919304041016563, "ring", false, 15822655434518384680], [8995469080876806959, "untrusted", false, 15028112852632671627]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-b5cd49eeda4acc02\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}