{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 5990689683127431354, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3733039266302241052], [3150220818285335163, "url", false, 15436067219720112665], [3191507132440681679, "serde_untagged", false, 14358956175397319096], [4071963112282141418, "serde_with", false, 12908481899909771520], [4899080583175475170, "semver", false, 18153861679191737180], [5986029879202738730, "log", false, 13613251818465215128], [6606131838865521726, "ctor", false, 14840447034527272579], [7170110829644101142, "json_patch", false, 2276501121301575578], [8319709847752024821, "uuid", false, 16141516712536101897], [9010263965687315507, "http", false, 11900686201987544601], [9451456094439810778, "regex", false, 11945308429598753897], [9556762810601084293, "brotli", false, 11078310508837083411], [9689903380558560274, "serde", false, 7268268971378764181], [10806645703491011684, "thiserror", false, 12471000282580757805], [11989259058781683633, "dunce", false, 11499366452444015799], [13625485746686963219, "anyhow", false, 10310713955744028308], [15367738274754116744, "serde_json", false, 5417323229602488537], [15609422047640926750, "toml", false, 14074032157265249958], [15622660310229662834, "walkdir", false, 15504782172643710012], [15932120279885307830, "memchr", false, 3671396833497195451], [17146114186171651583, "infer", false, 17600998024378644221], [17155886227862585100, "glob", false, 5839460457561943336], [17186037756130803222, "phf", false, 9821322461281348503]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-29c27eca3ddf44bd\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}