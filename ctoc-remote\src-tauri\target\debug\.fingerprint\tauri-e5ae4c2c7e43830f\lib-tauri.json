{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 6734270059689547341, "deps": [[40386456601120721, "percent_encoding", false, 10432675335327044733], [1200537532907108615, "url<PERSON><PERSON>n", false, 3733039266302241052], [2013030631243296465, "webview2_com", false, 3020859597941808250], [2671782512663819132, "tauri_utils", false, 12611128184557746340], [3150220818285335163, "url", false, 15436067219720112665], [3331586631144870129, "getrandom", false, 17732577132462432449], [3478659405401458569, "tokio", false, 17148441098418032753], [4143744114649553716, "raw_window_handle", false, 2349762867116203253], [4494683389616423722, "muda", false, 16080705465840363953], [4919829919303820331, "serialize_to_javascript", false, 13755851507601316307], [5986029879202738730, "log", false, 13613251818465215128], [6089812615193535349, "tauri_runtime", false, 11684447462034376327], [7573826311589115053, "tauri_macros", false, 12603429706660134172], [9010263965687315507, "http", false, 11900686201987544601], [9689903380558560274, "serde", false, 7268268971378764181], [10229185211513642314, "mime", false, 7465213439250967705], [10806645703491011684, "thiserror", false, 12471000282580757805], [11599800339996261026, "tauri_runtime_wry", false, 15682013745317126107], [11989259058781683633, "dunce", false, 11499366452444015799], [12565293087094287914, "window_vibrancy", false, 2845148322694018441], [12986574360607194341, "serde_repr", false, 3154859973344078384], [13077543566650298139, "heck", false, 1767662938505163294], [13625485746686963219, "anyhow", false, 10310713955744028308], [14039947826026167952, "build_script_build", false, 17964606449721194351], [14585479307175734061, "windows", false, 12606393980976928651], [15367738274754116744, "serde_json", false, 5417323229602488537], [16928111194414003569, "dirs", false, 728164042402155627], [17155886227862585100, "glob", false, 5839460457561943336]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-e5ae4c2c7e43830f\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}