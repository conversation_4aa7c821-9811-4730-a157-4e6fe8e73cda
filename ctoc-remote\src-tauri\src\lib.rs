// CtocSoft Remote Desktop Control Software
// Core modules
pub mod core;

use core::*;
use tauri::{Manager, State};
use std::sync::{Arc, Mutex};
use anyhow::Result;
use log::{info, error};

/// Application state
pub struct AppState {
    pub device_manager: Arc<Mutex<DeviceManager>>,
    pub security_manager: Arc<Mutex<SecurityManager>>,
    pub config_manager: Arc<Mutex<ConfigManager>>,
    pub signaling_client: Arc<Mutex<Option<SignalingClient>>>,
}

impl AppState {
    pub fn new() -> Result<Self> {
        Ok(Self {
            device_manager: Arc::new(Mutex::new(DeviceManager::new()?)),
            security_manager: Arc::new(Mutex::new(SecurityManager::new()?)),
            config_manager: Arc::new(Mutex::new(ConfigManager::new()?)),
            signaling_client: Arc::new(Mutex::new(None)),
        })
    }
}

// <PERSON><PERSON> commands
#[tauri::command]
async fn get_device_info(state: State<'_, AppState>) -> Result<DeviceInfo, String> {
    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;
    Ok(device_manager.get_device_info().clone())
}

#[tauri::command]
async fn generate_otp(state: State<'_, AppState>) -> Result<String, String> {
    let security_manager = state.security_manager.lock().map_err(|e| e.to_string())?;
    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;

    let otp = security_manager.generate_otp(device_manager.get_device_id())
        .map_err(|e| e.to_string())?;

    Ok(otp.code)
}

#[tauri::command]
async fn connect_to_signaling_server(
    state: State<'_, AppState>,
    server_url: String,
) -> Result<(), String> {
    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;
    let device_id = device_manager.get_device_id().to_string();
    drop(device_manager);

    let mut signaling_client = SignalingClient::new(server_url, device_id);
    signaling_client.connect().await.map_err(|e| e.to_string())?;

    let mut client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;
    *client_guard = Some(signaling_client);

    Ok(())
}

#[tauri::command]
async fn send_pair_request(
    state: State<'_, AppState>,
    target_device_id: String,
    otp: String,
) -> Result<(), String> {
    let client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;
    if let Some(client) = client_guard.as_ref() {
        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;
        Ok(())
    } else {
        Err("Not connected to signaling server".to_string())
    }
}

#[tauri::command]
async fn get_app_config(state: State<'_, AppState>) -> Result<AppConfig, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    Ok(config_manager.get_config().clone())
}

/// Main application entry point
pub fn run() {
    // Initialize logging
    env_logger::init();

    info!("Starting CtocSoft Remote Desktop Control Software");

    // Initialize application state
    let app_state = AppState::new().expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            get_device_info,
            generate_otp,
            connect_to_signaling_server,
            send_pair_request,
            get_app_config
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
