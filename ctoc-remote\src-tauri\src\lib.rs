// CtocSoft Remote Desktop Control Software
// Core modules
pub mod core;

use crate::core::{
    device::{<PERSON>ceManager, DeviceInfo},
    security::{SecurityManager, Otp},
    config::{ConfigManager, AppConfig},
    network::SignalingClient,
    webrtc::{WebRTCManager, SessionDescription, IceCandidate, ConnectionState},
};
use tauri::{Manager, State};
use std::sync::{Arc, Mutex};
use tokio::sync::Mutex as AsyncMutex;
use anyhow::Result;
use log::{info, error};

/// Application state
pub struct AppState {
    pub device_manager: Arc<Mutex<DeviceManager>>,
    pub security_manager: Arc<Mutex<SecurityManager>>,
    pub config_manager: Arc<Mutex<ConfigManager>>,
    pub signaling_client: Arc<Mutex<Option<SignalingClient>>>,
    pub webrtc_manager: Arc<AsyncMutex<Option<WebRTCManager>>>,
}

impl AppState {
    pub fn new() -> Result<Self> {
        Ok(Self {
            device_manager: Arc::new(Mutex::new(DeviceManager::new()?)),
            security_manager: Arc::new(Mutex::new(SecurityManager::new()?)),
            config_manager: Arc::new(Mutex::new(ConfigManager::new()?)),
            signaling_client: Arc::new(Mutex::new(None)),
            webrtc_manager: Arc::new(AsyncMutex::new(None)),
        })
    }
}

// Tauri commands
#[tauri::command]
async fn get_device_info(state: State<'_, AppState>) -> Result<DeviceInfo, String> {
    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;
    Ok(device_manager.get_device_info().clone())
}

#[tauri::command]
async fn generate_otp(state: State<'_, AppState>) -> Result<String, String> {
    let security_manager = state.security_manager.lock().map_err(|e| e.to_string())?;
    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;

    let otp = security_manager.generate_otp(device_manager.get_device_id())
        .map_err(|e| e.to_string())?;

    Ok(otp.code)
}

#[tauri::command]
async fn connect_to_signaling_server(
    state: State<'_, AppState>,
    server_url: String,
) -> Result<(), String> {
    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;
    let device_id = device_manager.get_device_id().to_string();
    drop(device_manager);

    let mut signaling_client = SignalingClient::new(server_url, device_id);
    signaling_client.connect().await.map_err(|e| e.to_string())?;

    let mut client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;
    *client_guard = Some(signaling_client);

    Ok(())
}

#[tauri::command]
async fn send_pair_request(
    state: State<'_, AppState>,
    target_device_id: String,
    otp: String,
) -> Result<(), String> {
    let client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;
    if let Some(client) = client_guard.as_ref() {
        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;
        Ok(())
    } else {
        Err("Not connected to signaling server".to_string())
    }
}

#[tauri::command]
async fn get_app_config(state: State<'_, AppState>) -> Result<AppConfig, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    Ok(config_manager.get_config().clone())
}

#[tauri::command]
async fn initialize_webrtc(state: State<'_, AppState>, is_controlling: bool) -> Result<(), String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    let config = config_manager.get_config();

    let stun_servers = config.network.stun_servers.clone();
    let turn_servers = config.network.turn_servers.iter()
        .map(|server| format!("turn:{}", server))
        .collect();

    let mut webrtc_manager = WebRTCManager::new(is_controlling);
    webrtc_manager.initialize(stun_servers, turn_servers).await
        .map_err(|e| e.to_string())?;

    let mut manager_guard = state.webrtc_manager.lock().await;
    *manager_guard = Some(webrtc_manager);

    info!("WebRTC initialized as {}", if is_controlling { "controlling" } else { "controlled" });
    Ok(())
}

#[tauri::command]
async fn create_webrtc_offer(state: State<'_, AppState>) -> Result<SessionDescription, String> {
    let manager_guard = state.webrtc_manager.lock().await;
    let webrtc_manager = manager_guard.as_ref()
        .ok_or_else(|| "WebRTC not initialized".to_string())?;

    // Create data channels for controlling side
    webrtc_manager.create_data_channel("screen").await.map_err(|e| e.to_string())?;
    webrtc_manager.create_data_channel("audio").await.map_err(|e| e.to_string())?;
    webrtc_manager.create_data_channel("input").await.map_err(|e| e.to_string())?;
    webrtc_manager.create_data_channel("control").await.map_err(|e| e.to_string())?;

    let offer = webrtc_manager.create_offer().await.map_err(|e| e.to_string())?;
    info!("WebRTC offer created");
    Ok(offer)
}

#[tauri::command]
async fn create_webrtc_answer(state: State<'_, AppState>) -> Result<SessionDescription, String> {
    let manager_guard = state.webrtc_manager.lock().await;
    let webrtc_manager = manager_guard.as_ref()
        .ok_or_else(|| "WebRTC not initialized".to_string())?;

    let answer = webrtc_manager.create_answer().await.map_err(|e| e.to_string())?;
    info!("WebRTC answer created");
    Ok(answer)
}

#[tauri::command]
async fn set_webrtc_remote_description(
    state: State<'_, AppState>,
    description: SessionDescription
) -> Result<(), String> {
    let manager_guard = state.webrtc_manager.lock().await;
    let webrtc_manager = manager_guard.as_ref()
        .ok_or_else(|| "WebRTC not initialized".to_string())?;

    webrtc_manager.set_remote_description(description).await.map_err(|e| e.to_string())?;
    info!("WebRTC remote description set");
    Ok(())
}

#[tauri::command]
async fn add_webrtc_ice_candidate(
    state: State<'_, AppState>,
    candidate: IceCandidate
) -> Result<(), String> {
    let manager_guard = state.webrtc_manager.lock().await;
    let webrtc_manager = manager_guard.as_ref()
        .ok_or_else(|| "WebRTC not initialized".to_string())?;

    webrtc_manager.add_ice_candidate(candidate).await.map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
async fn get_webrtc_connection_state(state: State<'_, AppState>) -> Result<ConnectionState, String> {
    let manager_guard = state.webrtc_manager.lock().await;
    let webrtc_manager = manager_guard.as_ref()
        .ok_or_else(|| "WebRTC not initialized".to_string())?;

    Ok(webrtc_manager.connection_state().await)
}

#[tauri::command]
async fn close_webrtc_connection(state: State<'_, AppState>) -> Result<(), String> {
    let mut manager_guard = state.webrtc_manager.lock().await;
    if let Some(webrtc_manager) = manager_guard.as_ref() {
        webrtc_manager.close().await.map_err(|e| e.to_string())?;
    }
    *manager_guard = None;
    info!("WebRTC connection closed");
    Ok(())
}

/// Simple test command
#[tauri::command]
async fn test_command() -> Result<String, String> {
    Ok("CtocSoft Remote Desktop is working!".to_string())
}

/// Main application entry point
pub fn run() {
    // Initialize logging
    env_logger::init();

    info!("Starting CtocSoft Remote Desktop Control Software");

    // Initialize application state
    let app_state = AppState::new().expect("Failed to initialize application state");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            test_command,
            get_device_info,
            generate_otp,
            connect_to_signaling_server,
            send_pair_request,
            get_app_config,
            initialize_webrtc,
            create_webrtc_offer,
            create_webrtc_answer,
            set_webrtc_remote_description,
            add_webrtc_ice_candidate,
            get_webrtc_connection_state,
            close_webrtc_connection
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
