{"$message_type":"diagnostic","message":"file not found for module `windows`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"src\\core\\capture.rs","byte_start":4201,"byte_end":4217,"line_start":158,"line_end":158,"column_start":1,"column_end":17,"is_primary":true,"text":[{"text":"pub mod windows;","highlight_start":1,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `windows`, create file \"src\\core\\capture\\windows.rs\" or \"src\\core\\capture\\windows\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod windows` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `windows`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\capture.rs:158:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod windows;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `windows`, create file \"src\\core\\capture\\windows.rs\" or \"src\\core\\capture\\windows\\mod.rs\"\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod windows` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `windows`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"src\\core\\input.rs","byte_start":4546,"byte_end":4562,"line_start":161,"line_end":161,"column_start":1,"column_end":17,"is_primary":true,"text":[{"text":"pub mod windows;","highlight_start":1,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `windows`, create file \"src\\core\\input\\windows.rs\" or \"src\\core\\input\\windows\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod windows` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `windows`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\input.rs:161:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod windows;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `windows`, create file \"src\\core\\input\\windows.rs\" or \"src\\core\\input\\windows\\mod.rs\"\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod windows` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\webrtc.rs","byte_start":21,"byte_end":27,"line_start":1,"line_end":1,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\core\\webrtc.rs","byte_start":19,"byte_end":27,"line_start":1,"line_end":1,"column_start":20,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":20,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\webrtc.rs","byte_start":12,"byte_end":13,"line_start":1,"line_end":1,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\core\\webrtc.rs","byte_start":27,"byte_end":28,"line_start":1,"line_end":1,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\webrtc.rs:1:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\webrtc.rs","byte_start":135,"byte_end":139,"line_start":5,"line_end":5,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"use log::{info, warn, error, debug};","highlight_start":17,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\webrtc.rs","byte_start":141,"byte_end":146,"line_start":5,"line_end":5,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"use log::{info, warn, error, debug};","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\core\\webrtc.rs","byte_start":133,"byte_end":146,"line_start":5,"line_end":5,"column_start":15,"column_end":28,"is_primary":true,"text":[{"text":"use log::{info, warn, error, debug};","highlight_start":15,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\webrtc.rs:5:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{info, warn, error, debug};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\capture.rs","byte_start":61,"byte_end":75,"line_start":3,"line_end":3,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\core\\capture.rs","byte_start":57,"byte_end":77,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use tokio::sync::mpsc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\capture.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"ambiguous glob re-exports","code":{"code":"ambiguous_glob_reexports","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\mod.rs","byte_start":244,"byte_end":254,"line_start":14,"line_end":14,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"pub use capture::*;","highlight_start":9,"highlight_end":19}],"label":"the name `windows` in the type namespace is first re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\mod.rs","byte_start":285,"byte_end":293,"line_start":16,"line_end":16,"column_start":9,"column_end":17,"is_primary":false,"text":[{"text":"pub use input::*;","highlight_start":9,"highlight_end":17}],"label":"but the name `windows` in the type namespace is also re-exported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(ambiguous_glob_reexports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: ambiguous glob re-exports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\mod.rs:14:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use capture::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mthe name `windows` in the type namespace is first re-exported here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use encoding::*;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use input::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mbut the name `windows` in the type namespace is also re-exported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(ambiguous_glob_reexports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Otp`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":173,"byte_end":176,"line_start":7,"line_end":7,"column_start":33,"column_end":36,"is_primary":true,"text":[{"text":"    security::{SecurityManager, Otp},","highlight_start":33,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":171,"byte_end":176,"line_start":7,"line_end":7,"column_start":31,"column_end":36,"is_primary":true,"text":[{"text":"    security::{SecurityManager, Otp},","highlight_start":31,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":155,"byte_end":156,"line_start":7,"line_end":7,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    security::{SecurityManager, Otp},","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":176,"byte_end":177,"line_start":7,"line_end":7,"column_start":36,"column_end":37,"is_primary":true,"text":[{"text":"    security::{SecurityManager, Otp},","highlight_start":36,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Otp`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:7:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    security::{SecurityManager, Otp},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":473,"byte_end":478,"line_start":16,"line_end":16,"column_start":17,"column_end":22,"is_primary":true,"text":[{"text":"use log::{info, error};","highlight_start":17,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":471,"byte_end":478,"line_start":16,"line_end":16,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use log::{info, error};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":466,"byte_end":467,"line_start":16,"line_end":16,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use log::{info, error};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\lib.rs","byte_start":478,"byte_end":479,"line_start":16,"line_end":16,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use log::{info, error};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:16:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{info, error};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of deprecated function `base64::encode`: Use Engine::encode","code":{"code":"deprecated","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\security.rs","byte_start":3705,"byte_end":3711,"line_start":116,"line_end":116,"column_start":17,"column_end":23,"is_primary":true,"text":[{"text":"        base64::encode(result)","highlight_start":17,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(deprecated)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: use of deprecated function `base64::encode`: Use Engine::encode\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\security.rs:116:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        base64::encode(result)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(deprecated)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`TurnServer` doesn't implement `std::fmt::Display`","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":3694,"byte_end":3700,"line_start":103,"line_end":103,"column_start":42,"column_end":48,"is_primary":true,"text":[{"text":"        .map(|server| format!(\"turn:{}\", server))","highlight_start":42,"highlight_end":48}],"label":"`TurnServer` cannot be formatted with the default formatter","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/rustc/6b00bc3880198600130e1cf62b8f8a93494488cc\\library\\alloc\\src\\macros.rs","byte_start":3921,"byte_end":3961,"line_start":108,"line_end":108,"column_start":33,"column_end":73,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3675,"byte_end":3701,"line_start":103,"line_end":103,"column_start":23,"column_end":49,"is_primary":false,"text":[{"text":"        .map(|server| format!(\"turn:{}\", server))","highlight_start":23,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"format!","def_site_span":{"file_name":"/rustc/6b00bc3880198600130e1cf62b8f8a93494488cc\\library\\alloc\\src\\macros.rs","byte_start":3807,"byte_end":3826,"line_start":105,"line_end":105,"column_start":1,"column_end":20,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__export::format_args!","def_site_span":{"file_name":"/rustc/6b00bc3880198600130e1cf62b8f8a93494488cc\\library\\core\\src\\macros\\mod.rs","byte_start":34889,"byte_end":34913,"line_start":1004,"line_end":1004,"column_start":5,"column_end":29,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the trait `std::fmt::Display` is not implemented for `TurnServer`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"in format strings you may be able to use `{:?}` (or {:#?} for pretty-print) instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `TurnServer` doesn't implement `std::fmt::Display`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:103:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map(|server| format!(\"turn:{}\", server))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`TurnServer` cannot be formatted with the default formatter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the trait `std::fmt::Display` is not implemented for `TurnServer`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: in format strings you may be able to use `{:?}` (or {:#?} for pretty-print) instead\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::__export::format_args` which comes from the expansion of the macro `format` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 0 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":3770,"byte_end":3772,"line_start":106,"line_end":106,"column_start":44,"column_end":46,"is_primary":false,"text":[{"text":"    let webrtc_manager = WebRTCManager::new().map_err(|e| e.to_string())?;","highlight_start":44,"highlight_end":46}],"label":"argument #1 of type `bool` is missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3752,"byte_end":3770,"line_start":106,"line_end":106,"column_start":26,"column_end":44,"is_primary":true,"text":[{"text":"    let webrtc_manager = WebRTCManager::new().map_err(|e| e.to_string())?;","highlight_start":26,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"src\\core\\webrtc.rs","byte_start":4564,"byte_end":4584,"line_start":145,"line_end":145,"column_start":16,"column_end":36,"is_primary":false,"text":[{"text":"    pub fn new(is_controlling: bool) -> Self {","highlight_start":16,"highlight_end":36}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\webrtc.rs","byte_start":4560,"byte_end":4563,"line_start":145,"line_end":145,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(is_controlling: bool) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the argument","code":null,"level":"help","spans":[{"file_name":"src\\lib.rs","byte_start":3770,"byte_end":3772,"line_start":106,"line_end":106,"column_start":44,"column_end":46,"is_primary":true,"text":[{"text":"    let webrtc_manager = WebRTCManager::new().map_err(|e| e.to_string())?;","highlight_start":44,"highlight_end":46}],"label":null,"suggested_replacement":"(/* bool */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: this function takes 1 argument but 0 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:106:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let webrtc_manager = WebRTCManager::new().map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14margument #1 of type `bool` is missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\webrtc.rs:145:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(is_controlling: bool) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    let webrtc_manager = WebRTCManager::new(\u001b[0m\u001b[0m\u001b[38;5;10m/* bool */\u001b[0m\u001b[0m).map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `map_err` found for struct `WebRTCManager` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":3773,"byte_end":3780,"line_start":106,"line_end":106,"column_start":47,"column_end":54,"is_primary":true,"text":[{"text":"    let webrtc_manager = WebRTCManager::new().map_err(|e| e.to_string())?;","highlight_start":47,"highlight_end":54}],"label":"method not found in `WebRTCManager`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\core\\webrtc.rs","byte_start":4166,"byte_end":4190,"line_start":135,"line_end":135,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"pub struct WebRTCManager {","highlight_start":1,"highlight_end":25}],"label":"method `map_err` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following traits define an item `map_err`, perhaps you need to implement one of them:\ncandidate #1: `TryFutureExt`\ncandidate #2: `TryStreamExt`\ncandidate #3: `winnow::parser::Parser`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no method named `map_err` found for struct `WebRTCManager` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:106:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let webrtc_manager = WebRTCManager::new().map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `WebRTCManager`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0msrc\\core\\webrtc.rs:135:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct WebRTCManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod `map_err` not found for this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the following traits define an item `map_err`, perhaps you need to implement one of them:\u001b[0m\n\u001b[0m            candidate #1: `TryFutureExt`\u001b[0m\n\u001b[0m            candidate #2: `TryStreamExt`\u001b[0m\n\u001b[0m            candidate #3: `winnow::parser::Parser`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1940,"byte_end":1957,"line_start":57,"line_end":57,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"future returned by `connect_to_signaling_server` is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tauri::generate_handler!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-macros-2.3.1\\src\\lib.rs","byte_start":2198,"byte_end":2255,"line_start":72,"line_end":72,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn generate_handler(item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"__cmd__connect_to_signaling_server!","def_site_span":{"file_name":"src\\lib.rs","byte_start":1940,"byte_end":1957,"line_start":57,"line_end":57,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, DeviceManager>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"future is not `Send` as this value is used across an await","code":null,"level":"note","spans":[{"file_name":"src\\lib.rs","byte_start":2368,"byte_end":2373,"line_start":67,"line_end":67,"column_start":32,"column_end":37,"is_primary":true,"text":[{"text":"    signaling_client.connect().await.map_err(|e| e.to_string())?;","highlight_start":32,"highlight_end":37}],"label":"await occurs here, with `device_manager` maybe used later","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2368,"byte_end":2373,"line_start":67,"line_end":67,"column_start":32,"column_end":37,"is_primary":false,"text":[{"text":"    signaling_client.connect().await.map_err(|e| e.to_string())?;","highlight_start":32,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2091,"byte_end":2105,"line_start":62,"line_end":62,"column_start":9,"column_end":23,"is_primary":false,"text":[{"text":"    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;","highlight_start":9,"highlight_end":23}],"label":"has type `std::sync::MutexGuard<'_, DeviceManager>` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `ResultFutureTag::future`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":8986,"byte_end":8992,"line_start":310,"line_end":310,"column_start":18,"column_end":24,"is_primary":false,"text":[{"text":"    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>","highlight_start":18,"highlight_end":24}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":9162,"byte_end":9166,"line_start":314,"line_end":314,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"      F: Future<Output = Result<T, E>> + Send,","highlight_start":42,"highlight_end":46}],"label":"required by this bound in `ResultFutureTag::future`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture returned by `connect_to_signaling_server` is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        .invoke_handler(tauri::generate_handler![\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m _________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            test_command,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            get_device_info,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            generate_otp,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            close_webrtc_connection\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ])\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, DeviceManager>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: future is not `Send` as this value is used across an await\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:67:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhas type `std::sync::MutexGuard<'_, DeviceManager>` which is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m67\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    signaling_client.connect().await.map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mawait occurs here, with `device_manager` maybe used later\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs:314:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m      F: Future<Output = Result<T, E>> + Send,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `__cmd__connect_to_signaling_server` which comes from the expansion of the macro `tauri::generate_handler` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":1940,"byte_end":1957,"line_start":57,"line_end":57,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"future returned by `connect_to_signaling_server` is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":1940,"byte_end":1957,"line_start":57,"line_end":57,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tauri::generate_handler!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-macros-2.3.1\\src\\lib.rs","byte_start":2198,"byte_end":2255,"line_start":72,"line_end":72,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn generate_handler(item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"__cmd__connect_to_signaling_server!","def_site_span":{"file_name":"src\\lib.rs","byte_start":1940,"byte_end":1957,"line_start":57,"line_end":57,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, DeviceManager>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"future is not `Send` as this value is used across an await","code":null,"level":"note","spans":[{"file_name":"src\\lib.rs","byte_start":2368,"byte_end":2373,"line_start":67,"line_end":67,"column_start":32,"column_end":37,"is_primary":true,"text":[{"text":"    signaling_client.connect().await.map_err(|e| e.to_string())?;","highlight_start":32,"highlight_end":37}],"label":"await occurs here, with `device_manager` maybe used later","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2368,"byte_end":2373,"line_start":67,"line_end":67,"column_start":32,"column_end":37,"is_primary":false,"text":[{"text":"    signaling_client.connect().await.map_err(|e| e.to_string())?;","highlight_start":32,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2091,"byte_end":2105,"line_start":62,"line_end":62,"column_start":9,"column_end":23,"is_primary":false,"text":[{"text":"    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;","highlight_start":9,"highlight_end":23}],"label":"has type `std::sync::MutexGuard<'_, DeviceManager>` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `ResultFutureTag::future`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":8986,"byte_end":8992,"line_start":310,"line_end":310,"column_start":18,"column_end":24,"is_primary":false,"text":[{"text":"    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>","highlight_start":18,"highlight_end":24}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":9162,"byte_end":9166,"line_start":314,"line_end":314,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"      F: Future<Output = Result<T, E>> + Send,","highlight_start":42,"highlight_end":46}],"label":"required by this bound in `ResultFutureTag::future`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:57:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture returned by `connect_to_signaling_server` is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        .invoke_handler(tauri::generate_handler![\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m _________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            test_command,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            get_device_info,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            generate_otp,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            close_webrtc_connection\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ])\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, DeviceManager>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: future is not `Send` as this value is used across an await\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:67:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let device_manager = state.device_manager.lock().map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhas type `std::sync::MutexGuard<'_, DeviceManager>` which is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m67\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    signaling_client.connect().await.map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mawait occurs here, with `device_manager` maybe used later\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs:314:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m      F: Future<Output = Result<T, E>> + Send,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `__cmd__connect_to_signaling_server` which comes from the expansion of the macro `tauri::generate_handler` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2557,"byte_end":2574,"line_start":75,"line_end":75,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"future returned by `send_pair_request` is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tauri::generate_handler!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-macros-2.3.1\\src\\lib.rs","byte_start":2198,"byte_end":2255,"line_start":72,"line_end":72,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn generate_handler(item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"__cmd__send_pair_request!","def_site_span":{"file_name":"src\\lib.rs","byte_start":2557,"byte_end":2574,"line_start":75,"line_end":75,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"future is not `Send` as this value is used across an await","code":null,"level":"note","spans":[{"file_name":"src\\lib.rs","byte_start":2904,"byte_end":2909,"line_start":83,"line_end":83,"column_start":57,"column_end":62,"is_primary":true,"text":[{"text":"        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;","highlight_start":57,"highlight_end":62}],"label":"await occurs here, with `client_guard` maybe used later","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2904,"byte_end":2909,"line_start":83,"line_end":83,"column_start":57,"column_end":62,"is_primary":false,"text":[{"text":"        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;","highlight_start":57,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2722,"byte_end":2734,"line_start":81,"line_end":81,"column_start":9,"column_end":21,"is_primary":false,"text":[{"text":"    let client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;","highlight_start":9,"highlight_end":21}],"label":"has type `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `ResultFutureTag::future`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":8986,"byte_end":8992,"line_start":310,"line_end":310,"column_start":18,"column_end":24,"is_primary":false,"text":[{"text":"    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>","highlight_start":18,"highlight_end":24}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":9162,"byte_end":9166,"line_start":314,"line_end":314,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"      F: Future<Output = Result<T, E>> + Send,","highlight_start":42,"highlight_end":46}],"label":"required by this bound in `ResultFutureTag::future`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:75:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture returned by `send_pair_request` is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        .invoke_handler(tauri::generate_handler![\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m _________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            test_command,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            get_device_info,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            generate_otp,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            close_webrtc_connection\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ])\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: future is not `Send` as this value is used across an await\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:83:57\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhas type `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>` which is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m82\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if let Some(client) = client_guard.as_ref() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m83\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mawait occurs here, with `client_guard` maybe used later\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs:314:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m      F: Future<Output = Result<T, E>> + Send,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `__cmd__send_pair_request` which comes from the expansion of the macro `tauri::generate_handler` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"future cannot be sent between threads safely","code":null,"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":2557,"byte_end":2574,"line_start":75,"line_end":75,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":"future returned by `send_pair_request` is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2557,"byte_end":2574,"line_start":75,"line_end":75,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":7705,"byte_end":8191,"line_start":212,"line_end":226,"column_start":25,"column_end":10,"is_primary":false,"text":[{"text":"        .invoke_handler(tauri::generate_handler![","highlight_start":25,"highlight_end":50},{"text":"            test_command,","highlight_start":1,"highlight_end":26},{"text":"            get_device_info,","highlight_start":1,"highlight_end":29},{"text":"            generate_otp,","highlight_start":1,"highlight_end":26},{"text":"            connect_to_signaling_server,","highlight_start":1,"highlight_end":41},{"text":"            send_pair_request,","highlight_start":1,"highlight_end":31},{"text":"            get_app_config,","highlight_start":1,"highlight_end":28},{"text":"            initialize_webrtc,","highlight_start":1,"highlight_end":31},{"text":"            create_webrtc_offer,","highlight_start":1,"highlight_end":33},{"text":"            create_webrtc_answer,","highlight_start":1,"highlight_end":34},{"text":"            set_webrtc_remote_description,","highlight_start":1,"highlight_end":43},{"text":"            add_webrtc_ice_candidate,","highlight_start":1,"highlight_end":38},{"text":"            get_webrtc_connection_state,","highlight_start":1,"highlight_end":41},{"text":"            close_webrtc_connection","highlight_start":1,"highlight_end":36},{"text":"        ])","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tauri::generate_handler!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-macros-2.3.1\\src\\lib.rs","byte_start":2198,"byte_end":2255,"line_start":72,"line_end":72,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn generate_handler(item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"__cmd__send_pair_request!","def_site_span":{"file_name":"src\\lib.rs","byte_start":2557,"byte_end":2574,"line_start":75,"line_end":75,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"#[tauri::command]","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"future is not `Send` as this value is used across an await","code":null,"level":"note","spans":[{"file_name":"src\\lib.rs","byte_start":2904,"byte_end":2909,"line_start":83,"line_end":83,"column_start":57,"column_end":62,"is_primary":true,"text":[{"text":"        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;","highlight_start":57,"highlight_end":62}],"label":"await occurs here, with `client_guard` maybe used later","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":2904,"byte_end":2909,"line_start":83,"line_end":83,"column_start":57,"column_end":62,"is_primary":false,"text":[{"text":"        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;","highlight_start":57,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"desugaring of `await` expression","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2722,"byte_end":2734,"line_start":81,"line_end":81,"column_start":9,"column_end":21,"is_primary":false,"text":[{"text":"    let client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;","highlight_start":9,"highlight_end":21}],"label":"has type `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>` which is not `Send`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"required by a bound in `ResultFutureTag::future`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":8986,"byte_end":8992,"line_start":310,"line_end":310,"column_start":18,"column_end":24,"is_primary":false,"text":[{"text":"    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>","highlight_start":18,"highlight_end":24}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs","byte_start":9162,"byte_end":9166,"line_start":314,"line_end":314,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"      F: Future<Output = Result<T, E>> + Send,","highlight_start":42,"highlight_end":46}],"label":"required by this bound in `ResultFutureTag::future`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: future cannot be sent between threads safely\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:75:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m#[tauri::command]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfuture returned by `send_pair_request` is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m212\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        .invoke_handler(tauri::generate_handler![\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m _________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            test_command,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            get_device_info,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            generate_otp,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            close_webrtc_connection\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m226\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ])\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14min this macro invocation\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: within `impl futures::Future<Output = Result<(), std::string::String>>`, the trait `std::marker::Send` is not implemented for `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: future is not `Send` as this value is used across an await\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:83:57\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let client_guard = state.signaling_client.lock().map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhas type `std::sync::MutexGuard<'_, std::option::Option<SignalingClient>>` which is not `Send`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m82\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    if let Some(client) = client_guard.as_ref() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m83\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        client.send_pair_request(target_device_id, otp).await.map_err(|e| e.to_string())?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mawait occurs here, with `client_guard` maybe used later\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\mirrors.ustc.edu.cn-38d0e5eb5da2abae\\tauri-2.6.2\\src\\ipc\\command.rs:314:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn future<T, E, F>(self, value: F) -> Result<InvokeResponseBody, InvokeError>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m314\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m      F: Future<Output = Result<T, E>> + Send,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `ResultFutureTag::future`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `__cmd__send_pair_request` which comes from the expansion of the macro `tauri::generate_handler` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Manager`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\lib.rs","byte_start":349,"byte_end":356,"line_start":12,"line_end":12,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"use tauri::{Manager, State};","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Manager`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:12:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tauri::{Manager, State};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `bandwidth_estimate`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\core\\encoding.rs","byte_start":5902,"byte_end":5920,"line_start":213,"line_end":213,"column_start":72,"column_end":90,"is_primary":true,"text":[{"text":"    pub fn update_network_stats(&mut self, rtt: u32, packet_loss: f32, bandwidth_estimate: Option<u32>) {","highlight_start":72,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\core\\encoding.rs","byte_start":5902,"byte_end":5920,"line_start":213,"line_end":213,"column_start":72,"column_end":90,"is_primary":true,"text":[{"text":"    pub fn update_network_stats(&mut self, rtt: u32, packet_loss: f32, bandwidth_estimate: Option<u32>) {","highlight_start":72,"highlight_end":90}],"label":null,"suggested_replacement":"_bandwidth_estimate","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `bandwidth_estimate`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\core\\encoding.rs:213:72\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m213\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn update_network_stats(&mut self, rtt: u32, packet_loss: f32, bandwidth_estimate: Option<u32>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_bandwidth_estimate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 9 previous errors; 9 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 9 previous errors; 9 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0277, E0583, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0061, E0277, E0583, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
