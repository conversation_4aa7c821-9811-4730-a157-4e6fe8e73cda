{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 10403337668460997990, "deps": [[2671782512663819132, "tauri_utils", false, 4718814954667557107], [3060637413840920116, "proc_macro2", false, 11079953401616781229], [3150220818285335163, "url", false, 1381137998325947695], [4899080583175475170, "semver", false, 13537410771240220741], [4974441333307933176, "syn", false, 11400399858999728954], [7170110829644101142, "json_patch", false, 5525281479707972769], [7392050791754369441, "ico", false, 17061906137100445804], [8319709847752024821, "uuid", false, 15193259577273070250], [9556762810601084293, "brotli", false, 13387534262866491279], [9689903380558560274, "serde", false, 4134122625824955924], [9857275760291862238, "sha2", false, 14927370607438812438], [10806645703491011684, "thiserror", false, 13945352358412116078], [12687914511023397207, "png", false, 14238738406424794142], [13077212702700853852, "base64", false, 16006401885286932603], [15367738274754116744, "serde_json", false, 12590173455974192260], [15622660310229662834, "walkdir", false, 10233985006651106905], [17990358020177143287, "quote", false, 6576768134661568835]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-8085ce2d6bcc8b6e\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}