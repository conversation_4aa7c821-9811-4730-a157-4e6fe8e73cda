use serde::{Deserialize, Serialize};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use futures::{SinkExt, StreamExt};
use anyhow::{Result, anyhow};
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use log::{info, error, debug};

/// Signaling message types for WebSocket communication
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum SignalingMessage {
    #[serde(rename = "login")]
    Login { device_id: String },
    
    #[serde(rename = "login-success")]
    LoginSuccess { message: String },
    
    #[serde(rename = "login-failed")]
    LoginFailed { reason: String },
    
    #[serde(rename = "pair-request")]
    PairRequest { target_device_id: String, otp: String },
    
    #[serde(rename = "pair-response")]
    PairResponse { status: String, message: String },
    
    #[serde(rename = "session-request")]
    SessionRequest { from_device_id: String, otp: String },
    
    #[serde(rename = "session-accept")]
    SessionAccept,
    
    #[serde(rename = "session-reject")]
    SessionReject,
    
    #[serde(rename = "session-ready")]
    SessionReady { session_id: String, peer_device_id: String },
    
    #[serde(rename = "offer")]
    Offer { sdp: SdpData },
    
    #[serde(rename = "answer")]
    Answer { sdp: SdpData },
    
    #[serde(rename = "ice-candidate")]
    IceCandidate { candidate: IceCandidateData },
    
    #[serde(rename = "end-session")]
    EndSession,
    
    #[serde(rename = "session-ended")]
    SessionEnded { reason: String },
    
    #[serde(rename = "error")]
    Error { message: String },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SdpData {
    #[serde(rename = "type")]
    pub sdp_type: String,
    pub sdp: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IceCandidateData {
    pub candidate: String,
    pub sdp_mid: Option<String>,
    pub sdp_m_line_index: Option<u16>,
}

/// Signaling client for WebSocket communication with signaling server
pub struct SignalingClient {
    server_url: String,
    device_id: String,
    message_sender: Option<mpsc::UnboundedSender<SignalingMessage>>,
    message_receiver: Arc<Mutex<Option<mpsc::UnboundedReceiver<SignalingMessage>>>>,
}

impl SignalingClient {
    pub fn new(server_url: String, device_id: String) -> Self {
        let (tx, rx) = mpsc::unbounded_channel();
        
        Self {
            server_url,
            device_id,
            message_sender: Some(tx),
            message_receiver: Arc::new(Mutex::new(Some(rx))),
        }
    }

    /// Connect to the signaling server
    pub async fn connect(&mut self) -> Result<()> {
        info!("Connecting to signaling server: {}", self.server_url);
        
        let (ws_stream, _) = connect_async(&self.server_url).await
            .map_err(|e| anyhow!("Failed to connect to signaling server: {}", e))?;

        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // Send login message
        let login_msg = SignalingMessage::Login {
            device_id: self.device_id.clone(),
        };
        
        let login_json = serde_json::to_string(&login_msg)?;
        ws_sender.send(Message::Text(login_json)).await?;

        // Handle incoming messages
        let message_sender = self.message_sender.take()
            .ok_or_else(|| anyhow!("Message sender already taken"))?;

        tokio::spawn(async move {
            while let Some(msg) = ws_receiver.next().await {
                match msg {
                    Ok(Message::Text(text)) => {
                        match serde_json::from_str::<SignalingMessage>(&text) {
                            Ok(signaling_msg) => {
                                debug!("Received signaling message: {:?}", signaling_msg);
                                if let Err(e) = message_sender.send(signaling_msg) {
                                    error!("Failed to forward signaling message: {}", e);
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("Failed to parse signaling message: {}", e);
                            }
                        }
                    }
                    Ok(Message::Close(_)) => {
                        info!("WebSocket connection closed");
                        break;
                    }
                    Err(e) => {
                        error!("WebSocket error: {}", e);
                        break;
                    }
                    _ => {}
                }
            }
        });

        info!("Connected to signaling server successfully");
        Ok(())
    }

    /// Send a signaling message
    pub async fn send_message(&self, message: SignalingMessage) -> Result<()> {
        // TODO: Implement message sending through WebSocket
        // For now, just log the message
        debug!("Sending signaling message: {:?}", message);
        Ok(())
    }

    /// Receive the next signaling message
    pub async fn receive_message(&self) -> Result<SignalingMessage> {
        let mut receiver_guard = self.message_receiver.lock().await;
        let receiver = receiver_guard.as_mut()
            .ok_or_else(|| anyhow!("Message receiver not available"))?;

        receiver.recv().await
            .ok_or_else(|| anyhow!("Message channel closed"))
    }

    /// Send a pairing request to another device
    pub async fn send_pair_request(&self, target_device_id: String, otp: String) -> Result<()> {
        let message = SignalingMessage::PairRequest {
            target_device_id,
            otp,
        };
        self.send_message(message).await
    }

    /// Accept a session request
    pub async fn accept_session(&self) -> Result<()> {
        let message = SignalingMessage::SessionAccept;
        self.send_message(message).await
    }

    /// Reject a session request
    pub async fn reject_session(&self) -> Result<()> {
        let message = SignalingMessage::SessionReject;
        self.send_message(message).await
    }

    /// Send WebRTC offer
    pub async fn send_offer(&self, sdp: SdpData) -> Result<()> {
        let message = SignalingMessage::Offer { sdp };
        self.send_message(message).await
    }

    /// Send WebRTC answer
    pub async fn send_answer(&self, sdp: SdpData) -> Result<()> {
        let message = SignalingMessage::Answer { sdp };
        self.send_message(message).await
    }

    /// Send ICE candidate
    pub async fn send_ice_candidate(&self, candidate: IceCandidateData) -> Result<()> {
        let message = SignalingMessage::IceCandidate { candidate };
        self.send_message(message).await
    }

    /// End the current session
    pub async fn end_session(&self) -> Result<()> {
        let message = SignalingMessage::EndSession;
        self.send_message(message).await
    }
}
