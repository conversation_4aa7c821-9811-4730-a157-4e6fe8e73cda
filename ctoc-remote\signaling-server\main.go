package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// Message types for signaling protocol
type MessageType string

const (
	MsgLogin          MessageType = "login"
	MsgLoginSuccess   MessageType = "login-success"
	MsgLoginFailed    MessageType = "login-failed"
	MsgPairRequest    MessageType = "pair-request"
	MsgPairResponse   MessageType = "pair-response"
	MsgSessionRequest MessageType = "session-request"
	MsgSessionAccept  MessageType = "session-accept"
	MsgSessionReject  MessageType = "session-reject"
	MsgSessionReady   MessageType = "session-ready"
	MsgOffer          MessageType = "offer"
	MsgAnswer         MessageType = "answer"
	MsgIceCandidate   MessageType = "ice-candidate"
	MsgEndSession     MessageType = "end-session"
	MsgSessionEnded   MessageType = "session-ended"
	MsgError          MessageType = "error"
)

// Client status
type ClientStatus string

const (
	StatusIdle      ClientStatus = "idle"
	StatusInSession ClientStatus = "in-session"
)

// Message structure
type Message struct {
	Type    MessageType     `json:"type"`
	Payload json.RawMessage `json:"payload,omitempty"`
}

// Payload structures
type LoginPayload struct {
	DeviceID string `json:"device_id"`
}

type LoginSuccessPayload struct {
	Message string `json:"message"`
}

type LoginFailedPayload struct {
	Reason string `json:"reason"`
}

type PairRequestPayload struct {
	TargetDeviceID string `json:"target_device_id"`
	OTP            string `json:"otp"`
}

type PairResponsePayload struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

type SessionRequestPayload struct {
	FromDeviceID string `json:"from_device_id"`
	OTP          string `json:"otp"`
}

type SessionReadyPayload struct {
	SessionID    string `json:"session_id"`
	PeerDeviceID string `json:"peer_device_id"`
}

type SDPPayload struct {
	SDP struct {
		Type string `json:"type"`
		SDP  string `json:"sdp"`
	} `json:"sdp"`
}

type ICECandidatePayload struct {
	Candidate struct {
		Candidate     string `json:"candidate"`
		SDPMid        string `json:"sdp_mid"`
		SDPMLineIndex int    `json:"sdp_m_line_index"`
	} `json:"candidate"`
}

type SessionEndedPayload struct {
	Reason string `json:"reason"`
}

type ErrorPayload struct {
	Message string `json:"message"`
}

// Client represents a connected client
type Client struct {
	ID         string
	DeviceID   string
	Connection *websocket.Conn
	Status     ClientStatus
	SessionID  string
	LastPing   time.Time
	mutex      sync.RWMutex
}

// Session represents an active session between two clients
type Session struct {
	ID            string
	ControllingID string
	ControlledID  string
	CreatedAt     time.Time
}

// SignalingServer manages WebSocket connections and signaling
type SignalingServer struct {
	clients       map[string]*Client        // connectionID -> Client
	deviceClients map[string]*Client        // deviceID -> Client
	sessions      map[string]*Session       // sessionID -> Session
	upgrader      websocket.Upgrader
	mutex         sync.RWMutex
	logger        *logrus.Logger
}

// NewSignalingServer creates a new signaling server
func NewSignalingServer() *SignalingServer {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	return &SignalingServer{
		clients:       make(map[string]*Client),
		deviceClients: make(map[string]*Client),
		sessions:      make(map[string]*Session),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins for development
			},
		},
		logger: logger,
	}
}

// HandleWebSocket handles WebSocket connections
func (s *SignalingServer) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		s.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}

	clientID := uuid.New().String()
	client := &Client{
		ID:         clientID,
		Connection: conn,
		Status:     StatusIdle,
		LastPing:   time.Now(),
	}

	s.mutex.Lock()
	s.clients[clientID] = client
	s.mutex.Unlock()

	s.logger.WithField("client_id", clientID).Info("New WebSocket connection")

	// Handle client messages
	go s.handleClient(client)

	// Start ping/pong heartbeat
	go s.heartbeat(client)
}

// handleClient processes messages from a client
func (s *SignalingServer) handleClient(client *Client) {
	defer func() {
		s.removeClient(client)
		client.Connection.Close()
	}()

	for {
		var msg Message
		err := client.Connection.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				s.logger.WithError(err).WithField("client_id", client.ID).Error("WebSocket error")
			}
			break
		}

		client.mutex.Lock()
		client.LastPing = time.Now()
		client.mutex.Unlock()

		s.logger.WithFields(logrus.Fields{
			"client_id": client.ID,
			"msg_type":  msg.Type,
		}).Debug("Received message")

		s.handleMessage(client, &msg)
	}
}

// handleMessage processes different message types
func (s *SignalingServer) handleMessage(client *Client, msg *Message) {
	switch msg.Type {
	case MsgLogin:
		s.handleLogin(client, msg.Payload)
	case MsgPairRequest:
		s.handlePairRequest(client, msg.Payload)
	case MsgSessionAccept:
		s.handleSessionAccept(client)
	case MsgSessionReject:
		s.handleSessionReject(client)
	case MsgOffer:
		s.handleOffer(client, msg.Payload)
	case MsgAnswer:
		s.handleAnswer(client, msg.Payload)
	case MsgIceCandidate:
		s.handleICECandidate(client, msg.Payload)
	case MsgEndSession:
		s.handleEndSession(client)
	default:
		s.sendError(client, fmt.Sprintf("Unknown message type: %s", msg.Type))
	}
}

// handleLogin processes login messages
func (s *SignalingServer) handleLogin(client *Client, payload json.RawMessage) {
	var loginPayload LoginPayload
	if err := json.Unmarshal(payload, &loginPayload); err != nil {
		s.sendError(client, "Invalid login payload")
		return
	}

	if loginPayload.DeviceID == "" {
		s.sendError(client, "Device ID is required")
		return
	}

	client.mutex.Lock()
	client.DeviceID = loginPayload.DeviceID
	client.mutex.Unlock()

	s.mutex.Lock()
	// Remove any existing client with the same device ID
	if existingClient, exists := s.deviceClients[loginPayload.DeviceID]; exists {
		s.logger.WithField("device_id", loginPayload.DeviceID).Info("Replacing existing client connection")
		existingClient.Connection.Close()
		delete(s.clients, existingClient.ID)
	}
	s.deviceClients[loginPayload.DeviceID] = client
	s.mutex.Unlock()

	s.sendMessage(client, MsgLoginSuccess, LoginSuccessPayload{
		Message: "Login successful",
	})

	s.logger.WithFields(logrus.Fields{
		"client_id": client.ID,
		"device_id": loginPayload.DeviceID,
	}).Info("Client logged in")
}

// handlePairRequest processes pairing requests
func (s *SignalingServer) handlePairRequest(client *Client, payload json.RawMessage) {
	var pairPayload PairRequestPayload
	if err := json.Unmarshal(payload, &pairPayload); err != nil {
		s.sendError(client, "Invalid pair request payload")
		return
	}

	s.mutex.RLock()
	targetClient, exists := s.deviceClients[pairPayload.TargetDeviceID]
	s.mutex.RUnlock()

	if !exists {
		s.sendMessage(client, MsgPairResponse, PairResponsePayload{
			Status:  "peer_not_found",
			Message: "Target device not found or offline",
		})
		return
	}

	// Forward session request to target client
	s.sendMessage(targetClient, MsgSessionRequest, SessionRequestPayload{
		FromDeviceID: client.DeviceID,
		OTP:          pairPayload.OTP,
	})

	s.sendMessage(client, MsgPairResponse, PairResponsePayload{
		Status:  "pairing_initiated",
		Message: "Pairing request sent to target device",
	})

	s.logger.WithFields(logrus.Fields{
		"from_device": client.DeviceID,
		"to_device":   pairPayload.TargetDeviceID,
	}).Info("Pairing request forwarded")
}

// handleSessionAccept processes session accept messages
func (s *SignalingServer) handleSessionAccept(client *Client) {
	// Find the pairing request (this is simplified - in production you'd track pending requests)
	sessionID := uuid.New().String()
	
	// Create session (simplified - you'd need to track the requesting client)
	session := &Session{
		ID:            sessionID,
		ControlledID:  client.DeviceID,
		// ControllingID would be set from the pending request
		CreatedAt:     time.Now(),
	}

	s.mutex.Lock()
	s.sessions[sessionID] = session
	client.Status = StatusInSession
	client.SessionID = sessionID
	s.mutex.Unlock()

	// Send session ready to both clients
	s.sendMessage(client, MsgSessionReady, SessionReadyPayload{
		SessionID:    sessionID,
		PeerDeviceID: session.ControllingID,
	})

	s.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"device_id":  client.DeviceID,
	}).Info("Session accepted")
}

// handleSessionReject processes session reject messages
func (s *SignalingServer) handleSessionReject(client *Client) {
	// In a full implementation, you'd find the requesting client and notify them
	s.logger.WithField("device_id", client.DeviceID).Info("Session rejected")
}

// Forward WebRTC signaling messages
func (s *SignalingServer) handleOffer(client *Client, payload json.RawMessage) {
	s.forwardToSessionPeer(client, MsgOffer, payload)
}

func (s *SignalingServer) handleAnswer(client *Client, payload json.RawMessage) {
	s.forwardToSessionPeer(client, MsgAnswer, payload)
}

func (s *SignalingServer) handleICECandidate(client *Client, payload json.RawMessage) {
	s.forwardToSessionPeer(client, MsgIceCandidate, payload)
}

// forwardToSessionPeer forwards a message to the session peer
func (s *SignalingServer) forwardToSessionPeer(client *Client, msgType MessageType, payload json.RawMessage) {
	if client.SessionID == "" {
		s.sendError(client, "Not in a session")
		return
	}

	s.mutex.RLock()
	session, exists := s.sessions[client.SessionID]
	s.mutex.RUnlock()

	if !exists {
		s.sendError(client, "Session not found")
		return
	}

	// Determine peer device ID
	var peerDeviceID string
	if session.ControllingID == client.DeviceID {
		peerDeviceID = session.ControlledID
	} else {
		peerDeviceID = session.ControllingID
	}

	s.mutex.RLock()
	peerClient, exists := s.deviceClients[peerDeviceID]
	s.mutex.RUnlock()

	if !exists {
		s.sendError(client, "Peer not found")
		return
	}

	// Forward the message
	msg := Message{
		Type:    msgType,
		Payload: payload,
	}
	peerClient.Connection.WriteJSON(msg)
}

// handleEndSession processes session end messages
func (s *SignalingServer) handleEndSession(client *Client) {
	if client.SessionID == "" {
		return
	}

	s.mutex.Lock()
	session, exists := s.sessions[client.SessionID]
	if exists {
		delete(s.sessions, client.SessionID)
	}
	client.Status = StatusIdle
	client.SessionID = ""
	s.mutex.Unlock()

	if exists {
		// Notify peer
		var peerDeviceID string
		if session.ControllingID == client.DeviceID {
			peerDeviceID = session.ControlledID
		} else {
			peerDeviceID = session.ControllingID
		}

		s.mutex.RLock()
		peerClient, peerExists := s.deviceClients[peerDeviceID]
		s.mutex.RUnlock()

		if peerExists {
			peerClient.mutex.Lock()
			peerClient.Status = StatusIdle
			peerClient.SessionID = ""
			peerClient.mutex.Unlock()

			s.sendMessage(peerClient, MsgSessionEnded, SessionEndedPayload{
				Reason: "user_terminated",
			})
		}
	}

	s.logger.WithField("session_id", client.SessionID).Info("Session ended")
}

// sendMessage sends a message to a client
func (s *SignalingServer) sendMessage(client *Client, msgType MessageType, payload interface{}) {
	payloadBytes, _ := json.Marshal(payload)
	msg := Message{
		Type:    msgType,
		Payload: payloadBytes,
	}
	client.Connection.WriteJSON(msg)
}

// sendError sends an error message to a client
func (s *SignalingServer) sendError(client *Client, message string) {
	s.sendMessage(client, MsgError, ErrorPayload{Message: message})
}

// removeClient removes a client from the server
func (s *SignalingServer) removeClient(client *Client) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	delete(s.clients, client.ID)
	if client.DeviceID != "" {
		delete(s.deviceClients, client.DeviceID)
	}

	// Clean up session if client was in one
	if client.SessionID != "" {
		if session, exists := s.sessions[client.SessionID]; exists {
			delete(s.sessions, client.SessionID)

			// Notify peer
			var peerDeviceID string
			if session.ControllingID == client.DeviceID {
				peerDeviceID = session.ControlledID
			} else {
				peerDeviceID = session.ControllingID
			}

			if peerClient, peerExists := s.deviceClients[peerDeviceID]; peerExists {
				peerClient.mutex.Lock()
				peerClient.Status = StatusIdle
				peerClient.SessionID = ""
				peerClient.mutex.Unlock()

				s.sendMessage(peerClient, MsgSessionEnded, SessionEndedPayload{
					Reason: "peer_disconnected",
				})
			}
		}
	}

	s.logger.WithFields(logrus.Fields{
		"client_id": client.ID,
		"device_id": client.DeviceID,
	}).Info("Client disconnected")
}

// heartbeat maintains connection health
func (s *SignalingServer) heartbeat(client *Client) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			client.mutex.RLock()
			lastPing := client.LastPing
			client.mutex.RUnlock()

			if time.Since(lastPing) > 60*time.Second {
				s.logger.WithField("client_id", client.ID).Info("Client heartbeat timeout")
				client.Connection.Close()
				return
			}

			if err := client.Connection.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func main() {
	var addr = flag.String("addr", ":8080", "http service address")
	flag.Parse()

	server := NewSignalingServer()

	http.HandleFunc("/ws", server.HandleWebSocket)
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	server.logger.WithField("address", *addr).Info("Starting signaling server")
	if err := http.ListenAndServe(*addr, nil); err != nil {
		server.logger.WithError(err).Fatal("Server failed to start")
	}
}
