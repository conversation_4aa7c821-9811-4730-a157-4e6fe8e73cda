{"rustc": 1842507548689473721, "features": "[\"alloc\", \"arithmetic\", \"der\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"signing\", \"spki\", \"std\", \"verifying\"]", "declared_features": "[\"alloc\", \"arithmetic\", \"default\", \"der\", \"dev\", \"digest\", \"hazmat\", \"pem\", \"pkcs8\", \"rfc6979\", \"serde\", \"serdect\", \"sha2\", \"signing\", \"spki\", \"std\", \"verifying\"]", "target": 5012119522651993362, "profile": 2241668132362809309, "path": 18403423209124564707, "deps": [[4234225094004207019, "rfc6979", false, 2604519128538103370], [10149501514950982522, "elliptic_curve", false, 13476177849642284778], [10800937535932116261, "der", false, 14138752726147190013], [11285023886693207100, "spki", false, 2451875679973309300], [13895928991373641935, "signature", false, 396103170853727898], [17475753849556516473, "digest", false, 11759510977646131552]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ecdsa-f9058f2c086b88dc\\dep-lib-ecdsa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}