{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 17730651729944172750, "deps": [[2671782512663819132, "tauri_utils", false, 4718814954667557107], [3060637413840920116, "proc_macro2", false, 11079953401616781229], [4974441333307933176, "syn", false, 11400399858999728954], [13077543566650298139, "heck", false, 11272322713274141626], [14455244907590647360, "tauri_codegen", false, 6904065151218079882], [17990358020177143287, "quote", false, 6576768134661568835]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-17418a89e5dfba5c\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}