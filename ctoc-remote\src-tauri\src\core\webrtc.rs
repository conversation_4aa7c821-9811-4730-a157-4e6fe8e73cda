use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
use webrtc::api::interceptor_registry::register_default_interceptors;
use webrtc::api::media_engine::MediaEngine;
use webrtc::api::APIBuilder;
use webrtc::data_channel::data_channel_message::DataChannelMessage;
use webrtc::data_channel::RTCDataChannel;
use webrtc::ice_transport::ice_candidate::{RTCIceCandidate, RTCIceCandidateInit};
use webrtc::ice_transport::ice_server::RTCIceServer;
use webrtc::interceptor::registry::Registry;
use webrtc::peer_connection::configuration::RTCConfiguration;
use webrtc::peer_connection::peer_connection_state::RTCPeerConnectionState;
use webrtc::peer_connection::sdp::session_description::RTCSessionDescription;
use webrtc::peer_connection::RTCPeerConnection;
use webrtc::rtp_transceiver::rtp_codec::RTCRtpCodecCapability;
use log::{info, warn, error, debug};

/// WebRTC connection states
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum ConnectionState {
    New,
    Connecting,
    Connected,
    Disconnected,
    Failed,
    Closed,
}

impl From<RTCPeerConnectionState> for ConnectionState {
    fn from(state: RTCPeerConnectionState) -> Self {
        match state {
            RTCPeerConnectionState::New => ConnectionState::New,
            RTCPeerConnectionState::Connecting => ConnectionState::Connecting,
            RTCPeerConnectionState::Connected => ConnectionState::Connected,
            RTCPeerConnectionState::Disconnected => ConnectionState::Disconnected,
            RTCPeerConnectionState::Failed => ConnectionState::Failed,
            RTCPeerConnectionState::Closed => ConnectionState::Closed,
            _ => ConnectionState::New,
        }
    }
}

/// WebRTC session description
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionDescription {
    pub sdp_type: String,
    pub sdp: String,
}

impl From<RTCSessionDescription> for SessionDescription {
    fn from(desc: RTCSessionDescription) -> Self {
        Self {
            sdp_type: desc.sdp_type.to_string(),
            sdp: desc.sdp,
        }
    }
}

impl Into<RTCSessionDescription> for SessionDescription {
    fn into(self) -> RTCSessionDescription {
        RTCSessionDescription {
            sdp_type: self.sdp_type.parse().unwrap_or_default(),
            sdp: self.sdp,
        }
    }
}

/// ICE candidate
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IceCandidate {
    pub candidate: String,
    pub sdp_mid: Option<String>,
    pub sdp_mline_index: Option<u16>,
}

impl From<RTCIceCandidate> for IceCandidate {
    fn from(candidate: RTCIceCandidate) -> Self {
        Self {
            candidate: candidate.to_string(),
            sdp_mid: candidate.sdp_mid,
            sdp_mline_index: candidate.sdp_mline_index,
        }
    }
}

impl Into<RTCIceCandidateInit> for IceCandidate {
    fn into(self) -> RTCIceCandidateInit {
        RTCIceCandidateInit {
            candidate: self.candidate,
            sdp_mid: self.sdp_mid,
            sdp_mline_index: self.sdp_mline_index,
            username_fragment: None,
        }
    }
}

/// Data channel message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum DataChannelMessageType {
    ScreenFrame { data: Vec<u8>, width: u32, height: u32, timestamp: u64 },
    AudioFrame { data: Vec<u8>, sample_rate: u32, channels: u16, timestamp: u64 },
    InputEvent { event_type: String, data: serde_json::Value },
    Control { command: String, params: serde_json::Value },
    Heartbeat { timestamp: u64 },
}

/// WebRTC connection events
#[derive(Debug, Clone)]
pub enum WebRTCEvent {
    ConnectionStateChanged(ConnectionState),
    DataChannelOpened(String),
    DataChannelClosed(String),
    DataChannelMessage(String, Vec<u8>),
    IceCandidate(IceCandidate),
    Error(String),
}

/// WebRTC connection manager
pub struct WebRTCManager {
    peer_connection: Option<Arc<RTCPeerConnection>>,
    data_channels: Arc<Mutex<std::collections::HashMap<String, Arc<RTCDataChannel>>>>,
    event_sender: mpsc::UnboundedSender<WebRTCEvent>,
    event_receiver: Arc<Mutex<mpsc::UnboundedReceiver<WebRTCEvent>>>,
    is_controlling: bool,
}

impl WebRTCManager {
    /// Create a new WebRTC manager
    pub fn new(is_controlling: bool) -> Self {
        let (event_sender, event_receiver) = mpsc::unbounded_channel();
        
        Self {
            peer_connection: None,
            data_channels: Arc::new(Mutex::new(std::collections::HashMap::new())),
            event_sender,
            event_receiver: Arc::new(Mutex::new(event_receiver)),
            is_controlling,
        }
    }

    /// Initialize WebRTC peer connection
    pub async fn initialize(&mut self, stun_servers: Vec<String>, turn_servers: Vec<String>) -> Result<()> {
        info!("Initializing WebRTC peer connection");

        // Create media engine
        let mut media_engine = MediaEngine::default();
        
        // Register default codecs
        media_engine.register_default_codecs()?;

        // Create interceptor registry
        let mut registry = Registry::new();
        registry = register_default_interceptors(registry, &mut media_engine)?;

        // Create API
        let api = APIBuilder::new()
            .with_media_engine(media_engine)
            .with_interceptor_registry(registry)
            .build();

        // Configure ICE servers
        let mut ice_servers = Vec::new();
        
        // Add STUN servers
        for stun_server in stun_servers {
            ice_servers.push(RTCIceServer {
                urls: vec![stun_server],
                username: String::new(),
                credential: String::new(),
                credential_type: Default::default(),
            });
        }

        // Add TURN servers (simplified - in production you'd parse credentials)
        for turn_server in turn_servers {
            ice_servers.push(RTCIceServer {
                urls: vec![turn_server],
                username: String::new(),
                credential: String::new(),
                credential_type: Default::default(),
            });
        }

        // Create peer connection configuration
        let config = RTCConfiguration {
            ice_servers,
            ..Default::default()
        };

        // Create peer connection
        let peer_connection = Arc::new(api.new_peer_connection(config).await?);
        
        // Set up event handlers
        self.setup_event_handlers(&peer_connection).await?;
        
        self.peer_connection = Some(peer_connection);
        
        info!("WebRTC peer connection initialized successfully");
        Ok(())
    }

    /// Set up event handlers for peer connection
    async fn setup_event_handlers(&self, pc: &Arc<RTCPeerConnection>) -> Result<()> {
        let event_sender = self.event_sender.clone();
        
        // Connection state change handler
        let event_sender_clone = event_sender.clone();
        pc.on_peer_connection_state_change(Box::new(move |state| {
            let event_sender = event_sender_clone.clone();
            Box::pin(async move {
                let connection_state = ConnectionState::from(state);
                info!("WebRTC connection state changed: {:?}", connection_state);
                let _ = event_sender.send(WebRTCEvent::ConnectionStateChanged(connection_state));
            })
        }));

        // ICE candidate handler
        let event_sender_clone = event_sender.clone();
        pc.on_ice_candidate(Box::new(move |candidate| {
            let event_sender = event_sender_clone.clone();
            Box::pin(async move {
                if let Some(candidate) = candidate {
                    let ice_candidate = IceCandidate::from(candidate);
                    debug!("New ICE candidate: {:?}", ice_candidate);
                    let _ = event_sender.send(WebRTCEvent::IceCandidate(ice_candidate));
                }
            })
        }));

        // Data channel handler
        let event_sender_clone = event_sender.clone();
        let data_channels = self.data_channels.clone();
        pc.on_data_channel(Box::new(move |data_channel| {
            let event_sender = event_sender_clone.clone();
            let data_channels = data_channels.clone();
            let channel_label = data_channel.label().to_string();
            
            Box::pin(async move {
                info!("Data channel opened: {}", channel_label);
                
                // Store data channel
                {
                    let mut channels = data_channels.lock().await;
                    channels.insert(channel_label.clone(), data_channel.clone());
                }
                
                let _ = event_sender.send(WebRTCEvent::DataChannelOpened(channel_label.clone()));
                
                // Set up data channel message handler
                let event_sender_clone = event_sender.clone();
                let channel_label_clone = channel_label.clone();
                data_channel.on_message(Box::new(move |msg| {
                    let event_sender = event_sender_clone.clone();
                    let channel_label = channel_label_clone.clone();
                    Box::pin(async move {
                        let _ = event_sender.send(WebRTCEvent::DataChannelMessage(
                            channel_label,
                            msg.data.to_vec(),
                        ));
                    })
                }));
                
                // Set up data channel close handler
                let event_sender_clone = event_sender.clone();
                let channel_label_clone = channel_label.clone();
                let data_channels_clone = data_channels.clone();
                data_channel.on_close(Box::new(move || {
                    let event_sender = event_sender_clone.clone();
                    let channel_label = channel_label_clone.clone();
                    let data_channels = data_channels_clone.clone();
                    Box::pin(async move {
                        info!("Data channel closed: {}", channel_label);
                        
                        // Remove data channel
                        {
                            let mut channels = data_channels.lock().await;
                            channels.remove(&channel_label);
                        }
                        
                        let _ = event_sender.send(WebRTCEvent::DataChannelClosed(channel_label));
                    })
                }));
            })
        }));

        Ok(())
    }

    /// Create data channel
    pub async fn create_data_channel(&self, label: &str) -> Result<()> {
        let pc = self.peer_connection.as_ref()
            .ok_or_else(|| anyhow!("Peer connection not initialized"))?;

        let data_channel = pc.create_data_channel(label, None).await?;
        
        {
            let mut channels = self.data_channels.lock().await;
            channels.insert(label.to_string(), data_channel);
        }
        
        info!("Data channel created: {}", label);
        Ok(())
    }

    /// Create offer
    pub async fn create_offer(&self) -> Result<SessionDescription> {
        let pc = self.peer_connection.as_ref()
            .ok_or_else(|| anyhow!("Peer connection not initialized"))?;

        let offer = pc.create_offer(None).await?;
        pc.set_local_description(offer.clone()).await?;
        
        info!("WebRTC offer created");
        Ok(SessionDescription::from(offer))
    }

    /// Create answer
    pub async fn create_answer(&self) -> Result<SessionDescription> {
        let pc = self.peer_connection.as_ref()
            .ok_or_else(|| anyhow!("Peer connection not initialized"))?;

        let answer = pc.create_answer(None).await?;
        pc.set_local_description(answer.clone()).await?;
        
        info!("WebRTC answer created");
        Ok(SessionDescription::from(answer))
    }

    /// Set remote description
    pub async fn set_remote_description(&self, desc: SessionDescription) -> Result<()> {
        let pc = self.peer_connection.as_ref()
            .ok_or_else(|| anyhow!("Peer connection not initialized"))?;

        let rtc_desc: RTCSessionDescription = desc.into();
        pc.set_remote_description(rtc_desc).await?;
        
        info!("Remote description set");
        Ok(())
    }

    /// Add ICE candidate
    pub async fn add_ice_candidate(&self, candidate: IceCandidate) -> Result<()> {
        let pc = self.peer_connection.as_ref()
            .ok_or_else(|| anyhow!("Peer connection not initialized"))?;

        let ice_candidate_init: RTCIceCandidateInit = candidate.into();
        pc.add_ice_candidate(ice_candidate_init).await?;
        
        debug!("ICE candidate added");
        Ok(())
    }

    /// Send data through data channel
    pub async fn send_data(&self, channel_label: &str, data: &[u8]) -> Result<()> {
        let channels = self.data_channels.lock().await;
        let channel = channels.get(channel_label)
            .ok_or_else(|| anyhow!("Data channel not found: {}", channel_label))?;

        channel.send(&DataChannelMessage::Binary(data.to_vec())).await?;
        Ok(())
    }

    /// Send structured message through data channel
    pub async fn send_message(&self, channel_label: &str, message: &DataChannelMessageType) -> Result<()> {
        let data = serde_json::to_vec(message)?;
        self.send_data(channel_label, &data).await
    }

    /// Get next event
    pub async fn next_event(&self) -> Option<WebRTCEvent> {
        let mut receiver = self.event_receiver.lock().await;
        receiver.recv().await
    }

    /// Close connection
    pub async fn close(&self) -> Result<()> {
        if let Some(pc) = &self.peer_connection {
            pc.close().await?;
            info!("WebRTC connection closed");
        }
        Ok(())
    }

    /// Get connection state
    pub async fn connection_state(&self) -> ConnectionState {
        if let Some(pc) = &self.peer_connection {
            ConnectionState::from(pc.connection_state())
        } else {
            ConnectionState::New
        }
    }

    /// Check if connection is established
    pub async fn is_connected(&self) -> bool {
        self.connection_state().await == ConnectionState::Connected
    }
}
