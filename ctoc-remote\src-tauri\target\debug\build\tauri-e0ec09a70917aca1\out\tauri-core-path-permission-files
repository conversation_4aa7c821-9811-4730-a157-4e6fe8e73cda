["\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\path\\autogenerated\\default.toml"]