{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 12739310782493568494, "deps": [[2671782512663819132, "tauri_utils", false, 12611128184557746340], [3150220818285335163, "url", false, 15436067219720112665], [4143744114649553716, "raw_window_handle", false, 2349762867116203253], [6089812615193535349, "build_script_build", false, 4383378228919459644], [7606335748176206944, "dpi", false, 7356066682231913605], [9010263965687315507, "http", false, 11900686201987544601], [9689903380558560274, "serde", false, 7268268971378764181], [10806645703491011684, "thiserror", false, 12471000282580757805], [14585479307175734061, "windows", false, 12606393980976928651], [15367738274754116744, "serde_json", false, 5417323229602488537], [16727543399706004146, "cookie", false, 5910098323866281247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-e8a65763e22139b2\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}