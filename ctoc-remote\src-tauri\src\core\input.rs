use serde::{Deserialize, Serialize};
use anyhow::Result;

/// Input event types for remote control
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum InputEvent {
    #[serde(rename = "<PERSON><PERSON>ove")]
    MouseMove { x: f64, y: f64 },
    
    #[serde(rename = "MouseDown")]
    MouseDown { button: <PERSON>B<PERSON><PERSON> },
    
    #[serde(rename = "<PERSON>Up")]
    MouseUp { button: MouseButton },
    
    #[serde(rename = "MouseWheel")]
    MouseWheel { delta_x: f64, delta_y: f64 },
    
    #[serde(rename = "KeyDown")]
    KeyDown { code: String, modifiers: KeyModifiers },
    
    #[serde(rename = "KeyUp")]
    KeyUp { code: String, modifiers: KeyModifiers },
    
    #[serde(rename = "Clipboard")]
    Clipboard { content: String },
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum MouseButton {
    #[serde(rename = "left")]
    Left,
    #[serde(rename = "right")]
    Right,
    #[serde(rename = "middle")]
    Middle,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct KeyModifiers {
    pub shift: bool,
    pub ctrl: bool,
    pub alt: bool,
    pub meta: bool,
}

impl Default for KeyModifiers {
    fn default() -> Self {
        Self {
            shift: false,
            ctrl: false,
            alt: false,
            meta: false,
        }
    }
}

/// Input capture trait for capturing local input events
#[async_trait::async_trait]
pub trait InputCapture: Send + Sync {
    async fn start_capture(&mut self) -> Result<()>;
    async fn stop_capture(&mut self) -> Result<()>;
    async fn get_next_event(&mut self) -> Result<Option<InputEvent>>;
}

/// Input injection trait for injecting remote input events
#[async_trait::async_trait]
pub trait InputInjection: Send + Sync {
    async fn inject_event(&mut self, event: &InputEvent) -> Result<()>;
    fn set_screen_size(&mut self, width: u32, height: u32);
    fn get_screen_size(&self) -> (u32, u32);
}

/// Input manager that handles both capture and injection
pub struct InputManager {
    capture: Option<Box<dyn InputCapture>>,
    injection: Option<Box<dyn InputInjection>>,
    screen_width: u32,
    screen_height: u32,
}

impl InputManager {
    pub fn new() -> Self {
        Self {
            capture: None,
            injection: None,
            screen_width: 1920,
            screen_height: 1080,
        }
    }

    pub fn set_capture(&mut self, capture: Box<dyn InputCapture>) {
        self.capture = Some(capture);
    }

    pub fn set_injection(&mut self, injection: Box<dyn InputInjection>) {
        self.injection = Some(injection);
    }

    pub fn set_screen_size(&mut self, width: u32, height: u32) {
        self.screen_width = width;
        self.screen_height = height;
        
        if let Some(injection) = &mut self.injection {
            injection.set_screen_size(width, height);
        }
    }

    pub async fn start_capture(&mut self) -> Result<()> {
        if let Some(capture) = &mut self.capture {
            capture.start_capture().await?;
        }
        Ok(())
    }

    pub async fn stop_capture(&mut self) -> Result<()> {
        if let Some(capture) = &mut self.capture {
            capture.stop_capture().await?;
        }
        Ok(())
    }

    pub async fn get_next_event(&mut self) -> Result<Option<InputEvent>> {
        if let Some(capture) = &mut self.capture {
            capture.get_next_event().await
        } else {
            Ok(None)
        }
    }

    pub async fn inject_event(&mut self, event: &InputEvent) -> Result<()> {
        if let Some(injection) = &mut self.injection {
            injection.inject_event(event).await?;
        }
        Ok(())
    }

    /// Transform coordinates from source screen to target screen
    pub fn transform_coordinates(&self, x: f64, y: f64, source_width: u32, source_height: u32) -> (f64, f64) {
        let scale_x = self.screen_width as f64 / source_width as f64;
        let scale_y = self.screen_height as f64 / source_height as f64;
        
        (x * scale_x, y * scale_y)
    }

    /// Normalize input event for transmission
    pub fn normalize_event(&self, event: InputEvent, canvas_width: u32, canvas_height: u32) -> InputEvent {
        match event {
            InputEvent::MouseMove { x, y } => {
                let (norm_x, norm_y) = self.transform_coordinates(x, y, canvas_width, canvas_height);
                InputEvent::MouseMove { x: norm_x, y: norm_y }
            }
            _ => event,
        }
    }
}

// Platform-specific implementations
#[cfg(target_os = "windows")]
pub mod windows;

#[cfg(target_os = "macos")]
pub mod macos;

#[cfg(target_os = "linux")]
pub mod linux;
