{"$schema": "https://schema.tauri.app/config/2", "productName": "CtocSoft Remote Desktop", "version": "0.1.0", "identifier": "com.ctocsoft.remote", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": true, "windows": [{"title": "CtocSoft Remote Desktop", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}