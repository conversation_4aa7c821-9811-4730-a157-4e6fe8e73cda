F:\test\ctoc-remote\src-tauri\target\debug\deps\ctoc_remote_lib-ccb5bf462cccb91b.d: src\lib.rs src\core\mod.rs src\core\device.rs src\core\network.rs src\core\webrtc.rs src\core\capture.rs src\core\encoding.rs src\core\input.rs src\core\security.rs src\core\config.rs F:\test\ctoc-remote\src-tauri\target\debug\build\ctoc-remote-56aa939b2de6f35f\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

F:\test\ctoc-remote\src-tauri\target\debug\deps\libctoc_remote_lib-ccb5bf462cccb91b.rmeta: src\lib.rs src\core\mod.rs src\core\device.rs src\core\network.rs src\core\webrtc.rs src\core\capture.rs src\core\encoding.rs src\core\input.rs src\core\security.rs src\core\config.rs F:\test\ctoc-remote\src-tauri\target\debug\build\ctoc-remote-56aa939b2de6f35f\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
src\core\mod.rs:
src\core\device.rs:
src\core\network.rs:
src\core\webrtc.rs:
src\core\capture.rs:
src\core\encoding.rs:
src\core\input.rs:
src\core\security.rs:
src\core\config.rs:
F:\test\ctoc-remote\src-tauri\target\debug\build\ctoc-remote-56aa939b2de6f35f\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=CtocSoft Team
# env-dep:CARGO_PKG_DESCRIPTION=CtocSoft Remote Desktop Control Software
# env-dep:CARGO_PKG_NAME=ctoc-remote
# env-dep:OUT_DIR=F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\ctoc-remote-56aa939b2de6f35f\\out
