{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8872929111123930462, "build_script_build", false, 15355402925905232647], [14039947826026167952, "build_script_build", false, 17964606449721194351], [16702348383442838006, "build_script_build", false, 2368402357218845022]], "local": [{"RerunIfChanged": {"output": "debug\\build\\ctoc-remote-56aa939b2de6f35f\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}