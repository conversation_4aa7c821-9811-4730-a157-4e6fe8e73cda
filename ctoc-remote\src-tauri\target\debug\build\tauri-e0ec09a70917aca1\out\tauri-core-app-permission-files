["\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\app\\autogenerated\\default.toml"]