import { invoke } from "@tauri-apps/api/core";

// Types
interface DeviceInfo {
  device_id: string;
  device_name: string;
  device_type: string;
  os_info: {
    platform: string;
    version: string;
    arch: string;
  };
  capabilities: {
    screen_capture: boolean;
    audio_capture: boolean;
    input_injection: boolean;
    hardware_encoding: string[];
  };
}

interface AppConfig {
  device: {
    device_name: string;
    auto_start: boolean;
    minimize_to_tray: boolean;
  };
  network: {
    signaling_server: string;
    stun_servers: string[];
    turn_servers: any[];
    connection_timeout: number;
    heartbeat_interval: number;
  };
  video: {
    default_width: number;
    default_height: number;
    default_framerate: number;
    default_bitrate: number;
    min_bitrate: number;
    max_bitrate: number;
    hardware_acceleration: boolean;
    codec_preference: string[];
  };
  audio: {
    enabled: boolean;
    sample_rate: number;
    channels: number;
    bitrate: number;
    codec_preference: string[];
  };
  security: {
    otp_expiry_seconds: number;
    max_connection_attempts: number;
    require_confirmation: boolean;
    auto_accept_trusted_devices: boolean;
  };
}

// Global state
let deviceInfo: DeviceInfo | null = null;
let appConfig: AppConfig | null = null;
let isConnected = false;
let currentOtp: string | null = null;
let otpTimer: number | null = null;

// DOM elements
let deviceIdEl: HTMLElement;
let connectionStatusEl: HTMLElement;
let serverUrlEl: HTMLInputElement;
let connectBtnEl: HTMLButtonElement;
let targetDeviceEl: HTMLInputElement;
let otpInputEl: HTMLInputElement;
let controlBtnEl: HTMLButtonElement;
let generateOtpBtnEl: HTMLButtonElement;
let otpDisplayEl: HTMLElement;
let otpCodeEl: HTMLElement;
let otpTimerEl: HTMLElement;
let statusLogEl: HTMLElement;
let desktopPanelEl: HTMLElement;
let connectionPanelEl: HTMLElement;
let remoteCanvasEl: HTMLCanvasElement;
let disconnectBtnEl: HTMLButtonElement;

// Utility functions
function logMessage(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const logEntry = document.createElement('div');
  logEntry.className = `log-entry log-${type}`;
  logEntry.textContent = `[${timestamp}] ${message}`;
  statusLogEl.appendChild(logEntry);
  statusLogEl.scrollTop = statusLogEl.scrollHeight;
}

function updateConnectionStatus(status: 'connected' | 'disconnected' | 'connecting') {
  connectionStatusEl.className = `status-${status}`;
  connectionStatusEl.textContent = status.charAt(0).toUpperCase() + status.slice(1);
  isConnected = status === 'connected';

  // Update button states
  connectBtnEl.disabled = status === 'connecting';
  controlBtnEl.disabled = !isConnected || !targetDeviceEl.value || !otpInputEl.value;
}

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// API functions
async function loadDeviceInfo() {
  try {
    deviceInfo = await invoke<DeviceInfo>("get_device_info");
    deviceIdEl.textContent = `Device ID: ${deviceInfo.device_id}`;
    logMessage(`Device loaded: ${deviceInfo.device_name} (${deviceInfo.device_id})`, 'success');
  } catch (error) {
    logMessage(`Failed to load device info: ${error}`, 'error');
  }
}

async function loadAppConfig() {
  try {
    appConfig = await invoke<AppConfig>("get_app_config");
    serverUrlEl.value = appConfig.network.signaling_server;
    logMessage('Configuration loaded successfully', 'success');
  } catch (error) {
    logMessage(`Failed to load configuration: ${error}`, 'error');
  }
}

async function connectToServer() {
  if (!serverUrlEl.value) {
    logMessage('Please enter a server URL', 'warning');
    return;
  }

  updateConnectionStatus('connecting');
  logMessage(`Connecting to ${serverUrlEl.value}...`, 'info');

  try {
    await invoke("connect_to_signaling_server", {
      serverUrl: serverUrlEl.value
    });
    updateConnectionStatus('connected');
    logMessage('Connected to signaling server', 'success');
  } catch (error) {
    updateConnectionStatus('disconnected');
    logMessage(`Connection failed: ${error}`, 'error');
  }
}

async function generateOtp() {
  try {
    const otp = await invoke<string>("generate_otp");
    currentOtp = otp;
    otpCodeEl.textContent = otp;
    otpDisplayEl.style.display = 'block';

    // Start countdown timer
    let timeLeft = 300; // 5 minutes
    otpTimerEl.textContent = `Expires in: ${formatTime(timeLeft)}`;

    if (otpTimer) clearInterval(otpTimer);
    otpTimer = setInterval(() => {
      timeLeft--;
      if (timeLeft <= 0) {
        clearInterval(otpTimer!);
        otpDisplayEl.style.display = 'none';
        currentOtp = null;
        logMessage('OTP expired', 'warning');
      } else {
        otpTimerEl.textContent = `Expires in: ${formatTime(timeLeft)}`;
      }
    }, 1000);

    logMessage(`OTP generated: ${otp}`, 'success');
  } catch (error) {
    logMessage(`Failed to generate OTP: ${error}`, 'error');
  }
}

async function startRemoteControl() {
  if (!targetDeviceEl.value || !otpInputEl.value) {
    logMessage('Please enter target device ID and OTP', 'warning');
    return;
  }

  try {
    await invoke("send_pair_request", {
      targetDeviceId: targetDeviceEl.value,
      otp: otpInputEl.value
    });
    logMessage(`Pairing request sent to ${targetDeviceEl.value}`, 'info');

    // Switch to desktop view
    connectionPanelEl.style.display = 'none';
    desktopPanelEl.style.display = 'block';
  } catch (error) {
    logMessage(`Failed to send pairing request: ${error}`, 'error');
  }
}

function disconnectRemoteControl() {
  // Switch back to connection view
  desktopPanelEl.style.display = 'none';
  connectionPanelEl.style.display = 'block';
  logMessage('Disconnected from remote session', 'info');
}

// Event handlers
function setupEventHandlers() {
  connectBtnEl.addEventListener('click', connectToServer);
  generateOtpBtnEl.addEventListener('click', generateOtp);
  controlBtnEl.addEventListener('click', startRemoteControl);
  disconnectBtnEl.addEventListener('click', disconnectRemoteControl);

  // Enable/disable control button based on input
  [targetDeviceEl, otpInputEl].forEach(el => {
    el.addEventListener('input', () => {
      controlBtnEl.disabled = !isConnected || !targetDeviceEl.value || !otpInputEl.value;
    });
  });

  // Canvas event handlers for remote input
  setupCanvasEventHandlers();
}

function setupCanvasEventHandlers() {
  // Mouse events
  remoteCanvasEl.addEventListener('mousemove', (e) => {
    // TODO: Send mouse move event to remote
  });

  remoteCanvasEl.addEventListener('mousedown', (e) => {
    // TODO: Send mouse down event to remote
  });

  remoteCanvasEl.addEventListener('mouseup', (e) => {
    // TODO: Send mouse up event to remote
  });

  remoteCanvasEl.addEventListener('wheel', (e) => {
    e.preventDefault();
    // TODO: Send wheel event to remote
  });

  // Keyboard events
  remoteCanvasEl.addEventListener('keydown', (e) => {
    e.preventDefault();
    // TODO: Send key down event to remote
  });

  remoteCanvasEl.addEventListener('keyup', (e) => {
    e.preventDefault();
    // TODO: Send key up event to remote
  });

  // Focus management
  remoteCanvasEl.addEventListener('click', () => {
    remoteCanvasEl.focus();
  });

  // Make canvas focusable
  remoteCanvasEl.tabIndex = 0;
}

// Initialize application
window.addEventListener("DOMContentLoaded", async () => {
  // Get DOM elements
  deviceIdEl = document.getElementById('device-id')!;
  connectionStatusEl = document.getElementById('connection-status')!;
  serverUrlEl = document.getElementById('server-url') as HTMLInputElement;
  connectBtnEl = document.getElementById('connect-btn') as HTMLButtonElement;
  targetDeviceEl = document.getElementById('target-device') as HTMLInputElement;
  otpInputEl = document.getElementById('otp-input') as HTMLInputElement;
  controlBtnEl = document.getElementById('control-btn') as HTMLButtonElement;
  generateOtpBtnEl = document.getElementById('generate-otp-btn') as HTMLButtonElement;
  otpDisplayEl = document.getElementById('otp-display')!;
  otpCodeEl = document.getElementById('otp-code')!;
  otpTimerEl = document.getElementById('otp-timer')!;
  statusLogEl = document.getElementById('status-log')!;
  desktopPanelEl = document.getElementById('desktop-panel')!;
  connectionPanelEl = document.getElementById('connection-panel')!;
  remoteCanvasEl = document.getElementById('remote-canvas') as HTMLCanvasElement;
  disconnectBtnEl = document.getElementById('disconnect-btn') as HTMLButtonElement;

  // Setup event handlers
  setupEventHandlers();

  // Initialize application
  logMessage('CtocSoft Remote Desktop starting...', 'info');
  await loadDeviceInfo();
  await loadAppConfig();
  logMessage('Application initialized successfully', 'success');
});
