# 下一代远程桌面软件：系统架构与技术栈选型

---

### 1. 引言与核心目标

本项目旨在打造一款秉持 “纯净、高速、安全” 核心理念的下一代远程桌面软件。为了超越现有解决方案，我们必须在系统架构和技术选型上奠定坚实的基础。本文档作为项目的核心技术纲领，将详细阐述经过审慎研究后确定的系统架构，并对各关键组件的技术栈选型进行最终决策与论证，确保所有技术决策都服务于实现极致性能、纯净体验和绝对安全的核心目标。

### 2. 系统架构：三层分布式模型

为兼顾性能、可扩展性与连接可靠性，我们确定采用一套现代化的三层分布式系统架构。该架构明确分离了信令与媒体数据流，旨在最大化P2P（点对点）直连成功率，从而最小化延迟和服务器成本。



架构组件及其职责:

| 组件 | 核心职责 | 交互说明 |
| :--- | :--- | :--- |
| 客户端 (Client) | - 作为控制端或被控端运行。<br>- 捕获/渲染屏幕、音频、输入事件。<br>- 执行数据编解码与端到端加密。<br>- 实现完整的ICE协议栈以协商P2P连接。 | - 与信令服务器交换会话信息和网络候选地址。<br>- 优先与对端客户端建立直接的媒体数据流。<br>- 在直连失败时，通过中继服务器转发媒体数据。 |
| 信令服务器 (Signaling Server) | - 连接的“交通指挥官”。<br>- 处理用户认证、设备ID/配对码验证。<br>- 撮合客户端，交换建立P2P连接所需的元数据（SDP）。<br>- 转发ICE候选地址，辅助NAT穿透。 | - 仅处理轻量级的信令数据（如JSON消息）。<br>- 绝不处理任何媒体流数据，保障用户隐私和服务器性能。 |
| 中继服务器 (Relay Server) | - 连接的“最后保障”。<br>- 提供标准的STUN服务，帮助客户端发现其公网IP和NAT类型。<br>- 提供标准的TURN服务，在P2P直连失败时，作为数据中继节点。 | - 当客户端（尤其是处于对称型NAT后）无法建立直接P2P连接时，所有媒体流将通过此服务器进行加密转发。 |

数据流分离原则:

- 信令流 (Signaling Flow): 客户端与信令服务器之间通过安全的WebSocket连接进行通信。此数据流轻量、低频，用于协调和建立会话。
- 媒体流 (Media Flow): 这是承载远程桌面视频、音频和输入指令的核心数据流。架构设计的首要目标是让媒体流通过 DTLS 加密的P2P通道在客户端之间直接传输。只有在P2P失败的情况下，媒体流才会通过中继服务器转发。这种分离确保了最低的延迟和最高的隐私保护。

### 3. 技术栈选型与论证

基于深入的技术研究和对项目目标的严格对齐，我们为各组件做出如下最终技术选型。

#### 3.1 客户端核心逻辑：Rust

* 决策： 选用 Rust 作为客户端核心逻辑的开发语言。
* 论证：
    *   极致性能与内存安全： 远程桌面软件的实时性要求极高。Rust的“零成本抽象”和对底层内存的精确控制，使其性能媲美C++，同时其所有权和借用检查器在编译时就根除了空指针、数据竞争等内存安全问题。这对于处理高帧率视频流和实时输入响应至关重要，能有效避免由内存错误导致的崩溃或安全漏洞。
    *   无畏并发： Rust的并发模型保证了线程安全，使得在多核CPU上并行处理屏幕捕获、编码、网络发送和UI渲染等多个任务变得简单而安全，不会引入传统多线程编程的复杂性和风险。
    *   生态系统： Rust拥有现代化的包管理工具Cargo和丰富的第三方库生态，尤其在网络编程、异步处理（`Tokio`）和跨平台开发领域表现出色。

#### 3.2 客户端UI框架：Tauri (基于Rust)

* 决策： 选用 Tauri 构建跨平台的图形用户界面。
* 论证：
    *   轻量级与性能： 与Electron等框架不同，Tauri不捆绑Node.js运行时，而是使用操作系统的原生WebView，从而大大减小了最终应用程序的体积和内存占用，完美契合我们“纯净”的设计理念。
    *   与核心逻辑无缝集成： Tauri的后端是纯粹的Rust，可以直接调用我们用Rust编写的核心业务逻辑，无需复杂的语言间（FFI）绑定。这种同构技术栈简化了开发、调试和构建流程，提升了整体性能和稳定性。
    *   安全性： Tauri提供了强大的安全模型，能够精细控制前端WebView对系统API的访问权限，有效隔离UI和核心逻辑，降低了潜在的攻击面。
    *   跨平台一致性： 支持Windows、macOS和Linux，能够以最小的平台特定代码实现一致的用户体验。

#### 3.3 信令服务器：Go

* 决策： 选用 Go 语言开发信令服务器。
* 论证：
    *   天生高并发： 信令服务器需要处理海量的、长连接的客户端请求。Go语言的Goroutine和Channel机制，使其能够以极低的资源开销轻松管理数十万并发连接，完美匹配信令服务器高I/O、高并发的场景。
    *   强大的网络库： Go标准库提供了强大且易用的网络编程API，特别是对于构建WebSocket服务器，社区有大量成熟、高性能的库（如`gorilla/websocket`）可供选择，极大地加速了开发进程。
    *   部署简便： Go程序可以编译成单个静态链接的二进制文件，不依赖任何外部运行时，部署过程极其简单，非常适合容器化（Docker）和云原生环境。

#### 3.4 中继服务器：coturn

* 决策： 部署业界标准的开源项目 coturn作为STUN/TURN中继服务器。
* 论证：
    *   成熟与稳定： `coturn` 是一个经过全球大规模生产环境验证的STUN/TURN服务器实现，被广泛应用于WebRTC等实时通信领域。其稳定性和可靠性远超自研方案。
    *   协议完整性： 它完整实现了RFC 5389 (STUN), RFC 5766 (TURN), 以及其他相关扩展标准。这意味着它能够处理各种复杂的NAT场景，包括最棘手的对称型NAT，从而最大化我们远程连接的成功率。
    *   专注核心业务： 采用`coturn`可以让我们避免在复杂的NAT穿透协议上“重新发明轮子”，将宝贵的研发资源集中在远程桌面的核心功能和用户体验优化上。部署和配置`coturn`相对简单，能够快速搭建起可靠的中继服务。

### 4. 结论

通过采用 Rust + Tauri 构建高性能、安全、轻量级的客户端，以 Go 语言打造高并发、可扩展的信令服务器，并借助 coturn 提供稳定可靠的NAT穿透中继服务，我们构建了一个技术上先进且逻辑上严谨的系统。

此套架构与技术栈相辅相成，为实现项目的三大核心目标——纯净、高速、安全——奠定了坚不可摧的技术基石。它不仅确保了端到端的极致性能和低延迟，还通过架构设计和技术选型从根本上保障了用户数据的隐私与安全，最终将为用户带来前所未有的远程桌面体验。
# 关键技术挑战与攻坚策略

---

### 摘要

本文档旨在为下一代远程桌面软件的核心技术攻坚提供一份详尽的、可执行的策略指南。基于已确定的系统架构与技术选型，我们识别出四大关键技术挑战：低延迟屏幕捕获与编码、复杂NAT网络穿透、动态码率调整及端到端加密。针对每一项挑战，本文将深入剖析其技术原理，并提出具体的、经过论证的攻坚策略与实现路径，以确保项目目标的达成：构建一款纯净、高速、安全的用户体验卓越的远程桌面产品。

---

### 1. 挑战一：低延迟屏幕捕获与编码

核心问题： 如何在高帧率（例如60FPS）下捕获屏幕内容，同时将CPU占用降至最低，为编码和网络传输预留充足的系统资源，从而实现“零感延迟”的视觉反馈？

#### 1.1. 攻坚策略：拥抱原生GPU加速管线

我们的核心策略是构建一个以GPU为中心的捕获与编码管线，最大限度地减少CPU的介入和内存拷贝。

1.  废弃传统GDI，采用原生图形API捕获：
    *   传统的GDI `BitBlt` 方式在现代桌面环境下（尤其开启Aero特效后）效率低下，CPU占用高，且存在鼠标闪烁等问题。
    *   我们的选择是针对不同平台采用最高效的原生API。

| 平台 | 首选捕获技术 | 核心优势 | 备用/兼容方案 |
| :--- | :--- | :--- | :--- |
| Windows 8+ | DXGI Desktop Duplication API | 直接利用GPU进行桌面复制，CPU占用极低，性能卓越，是实现高性能捕获的业界标准。 | GDI `BitBlt` (仅用于兼容Windows 7及更早版本) |
| macOS 13+ | ScreenCaptureKit | 现代化的专用框架，提供应用、窗口、显示器级别的精细捕获，效率高，原生支持音频同步。 | `AVFoundation` 或 `Core Graphics` (作为备用) |

2.  实施“脏矩形”更新检测，实现增量编码：
    *   全屏编码在静态场景下是巨大的资源浪费。我们必须实现增量更新，只对屏幕上发生变化的部分进行编码和传输。
    *   实现路径：
        *   优先使用API原生支持： `DXGI Desktop Duplication` 的 `GetFrameDirtyRects()` 方法可以直接提供更新区域列表。
        *   实现备用差分算法： 考虑到`GetFrameDirtyRects`在某些系统版本中可能返回整个屏幕，必须实现一个备用方案：在内存中保留前一帧的副本，通过快速的内存比较算法，自行计算出当前帧的“脏矩形”区域。此方案也适用于macOS。

3.  强制硬件加速编码，软件编码作为后备：
    *   视频编码是CPU密集型任务。将此任务卸载到GPU的专用硬件编码器（ASIC）是降低延迟和CPU占用的关键。
    *   技术实现： 客户端启动时，必须动态检测并优先初始化平台提供的硬件编码器。

| 平台 | 硬件加速技术 | 推荐Rust Crate/库 | 后备软件编码器 |
| :--- | :--- | :--- | :--- |
| Windows (NVIDIA) | NVENC | `nvenc-rs` | `openh264` |
| Windows (Intel) | Quick Sync Video (QSV) | (需封装 `libmfx` 或FFmpeg绑定) | `openh264` |
| Windows (AMD) | AMF/VCN | (需封装AMF SDK或FFmpeg绑定) | `openh264` |
| macOS | VideoToolbox | `video-toolbox` crate | `openh264` |

> 实现结论： 我们的捕获编码流程应为：[DXGI/ScreenCaptureKit GPU捕获] -> [GPU内脏矩形检测] -> [将脏矩形区域送入硬件编码器] -> [获取H.264码流]。整个流程尽可能在GPU内部完成，避免昂贵的GPU->CPU数据拷贝，从而实现极致性能。

---

### 2. 挑战二：复杂NAT网络穿透

核心问题： 如何在互联网复杂的网络拓扑（特别是多层NAT和对称型NAT）下，确保客户端之间P2P（点对点）直连的成功率超过95%，并在直连失败时提供可靠的连接保障？

#### 2.1. 攻坚策略：完整实施ICE协议并部署企业级中继服务

我们将严格遵循WebRTC标准，完整实施ICE（Interactive Connectivity Establishment）协议，并辅以强大的`coturn`中继服务器作为后盾。

1.  完整实现ICE流程：
    整个过程由信令服务器协调，客户端执行。
    1.  候选地址收集 (Candidate Gathering): 客户端启动时，必须收集所有类型的可用地址：
        *   主机地址 (Host): 本地的内网IP地址。
        *   服务器反射地址 (Server-Reflexive): 通过向STUN服务器发送请求，获取到的NAT设备公网IP和端口。
        *   中继地址 (Relayed): 通过向TURN服务器请求分配，获得的中继服务器地址和端口。
    2.  信令交换 (Signaling): 客户端将收集到的所有候选地址，通过已建立的WebSocket连接，利用信令服务器定义的`offer`/`answer`/`ice-candidate`消息，与对端进行交换。
    3.  连通性检查 (Connectivity Checks): 双方拿到对方的候选地址列表后，开始并行地对所有可能的地址对（如：我的Host地址 -> 对方的srflx地址）发送STUN绑定请求，进行“打洞”尝试。
    4.  路径选择与连接建立: ICE协议栈会根据连通性检查的结果，自动选择优先级最高（通常是 Host > Server-Reflexive > Relayed）且验证通过的路径来建立数据流。

2.  部署与利用coturn作为STUN/TURN服务器：
    *   决策： 我们将部署业界标准、高性能的开源项目 `coturn` 作为我们统一的STUN和TURN服务器。自研STUN/TURN服务器费时费力且难以保证稳定性和对各种复杂NAT场景的兼容性。
    *   STUN的角色: 帮助客户端发现其公网IP地址和NAT类型，是实现P2P直连的基础。
    *   TURN的角色:
        > TURN是实现我们>95%连接成功率目标的 “最后一道防线”。当客户端处于最严格的对称型NAT之后，P2P打洞会失败。此时，所有数据将通过加密的方式经由`coturn`服务器进行中继转发，确保连接的最终可达性。

3.  配置示例 (`turnserver.conf`):
    ```conf
    # TURN服务器监听的公网IP和端口
    listening-ip=YOUR_SERVER_PUBLIC_IP
    listening-port=3478
    
    # 客户端通过此公网IP访问
    external-ip=YOUR_SERVER_PUBLIC_IP
    
    # 安全认证配置
    realm=yourcompany.com
    user=your_user:your_password
    
    # 日志与安全
    log-file=/var/log/coturn.log
    no-multicast-peers
    no-cli
    ```

> 实现结论： 通过在客户端严格实现ICE协议的全部流程，并投资部署稳定、高可用的`coturn`集群，我们可以系统性地解决NAT穿透问题。我们的目标是，让95%以上的连接享受P2P的低延迟，同时让剩下5%的“疑难杂症”网络环境用户也能获得稳定可靠的连接体验。

---

### 3. 挑战三：动态码率调整（ABR）

核心问题： 如何设计一个智能的算法，使其能够实时感知网络质量的细微变化，并动态调整视频码率和分辨率，从而在“保证流畅不卡顿”和“追求高清画质”之间找到最佳平衡点？

#### 3.1. 攻坚策略：实现基于延迟和丢包反馈的自适应码率算法

我们将设计一套受Google Congestion Control (GCC) 算法启发的自适应码率（ABR）控制器。该控制器运行在被控端，根据控制端定期反馈的网络质量报告来做出决策。

1.  网络状态监测（输入）：
    控制端必须定期（例如每秒）通过WebRTC数据通道向被控端发送包含以下关键指标的反馈报告：
    *   往返时间 (RTT): 网络延迟的核心指标。
    *   丢包率 (Packet Loss): 网络拥塞的直接体现。
    *   可用带宽估计 (Bandwidth Estimation): 由接收端估算的下行带宽。

2.  ABR算法核心逻辑（状态机+AIMD）：
    我们的算法将网络状态分为三类，并根据状态和具体指标采取不同的调整策略（加性增，乘性减 - AIMD）。

| 网络状态 | 触发条件 | 码率调整策略 | 目标 |
| :--- | :--- | :--- | :--- |
| 过载 (Over-use) | 1. RTT持续显著增加<br>2. 丢包率 > 10% | 乘性降低 (Multiplicative Decrease)<br>`target_bitrate *= 0.85` | 快速降低码率，迅速缓解网络拥塞，优先保障流畅度。 |
| 临界 (Normal) | 1. RTT稳定<br>2. 2% < 丢包率 <= 10% | 保持不变 (Hold)<br>`target_bitrate = current_bitrate` | 网络处于敏感状态，保持观察，避免因盲目提速导致拥塞。 |
| 欠载 (Under-use) | 1. RTT稳定或下降<br>2. 丢包率 < 2% | 加性增加 (Additive Increase)<br>`target_bitrate += small_step` | 网络状况良好，缓慢、线性地增加码率，以探测更高的可用带宽，提升画质。 |

3.  动态分辨率调整（可选高级优化）：
    *   当码率被持续压低到一个阈值以下（例如低于1 Mbps），单纯降低码率会导致画面马赛克严重。
    *   此时，控制器应主动降低视频分辨率（例如从1080p降至720p），并重新设置一个适合该分辨率的基准码率。
    *   当网络恢复，码率持续提升并超过一个较高的阈值时，再尝试恢复到原始分辨率。

> 实现结论： 这套ABR算法将使我们的远程桌面软件具备“韧性”。它能够在网络波动时牺牲部分画质以换取用户的操作不被中断，在网络恢复时又能自动将画质提升到最佳水平，从而实现真正智能、自适应的流畅体验。

---

### 4. 挑战四：端到端加密（E2EE）

核心问题： 如何确保从用户键盘敲击、鼠标移动，到剪贴板内容和文件传输的所有数据，在客户端到客户端的传输过程中都是绝对安全的，即使信令服务器或网络链路被攻破，也无法被窃取和解密？

#### 4.1. 攻坚策略：强制执行分层加密，确保无可信第三方

我们的安全策略建立在“零信任”原则之上，通过对不同通道实施强制加密，构建一个纵深防御体系，实现真正的端到端加密。

1.  信令通道加密：TLS 1.3
    *   要求： 客户端与信令服务器之间的所有通信，必须通过安全的WebSocket协议（`WSS://`）进行。
    *   保障： WSS强制使用TLS加密。这意味着，用于建立连接的`SDP`和`ICE`候选地址等元数据在传输过程中是加密的，防止信令被窃听或篡改。但这不是端到端加密，因为信令服务器可以解密这些信息。

2.  数据通道加密：DTLS (核心)
    *   要求： 所有客户端之间的WebRTC数据通道（Data Channels）和媒体通道（Media Streams），必须强制启用并完成DTLS（数据报传输层安全）握手。
    *   保障： 这是实现端到端加密的关键。DTLS握手过程发生在P2P连接建立阶段，通信双方直接协商出会话密钥，此密钥对信令服务器和任何中间节点都是未知的。
    *   加密范围：
        *   媒体流 (SRTP): DTLS协商出的密钥将被用于派生SRTP（安全实时传输协议）的密钥，对所有视频和音频流进行加密。
        *   数据通道 (SCTP over DTLS): 承载键鼠、剪贴板、文件传输等所有控制和数据信息的通道，其底层传输协议SCTP被完整包裹在DTLS隧道内。

数据加密流示意图：
```
+----------------+        TLS        +------------------+        TLS        +----------------+
|  控制端 Client  | <---------------> |  信令服务器      | <---------------> |  被控端 Client  |
| (Controlling)  |  (WSS Encrypted)  | (Signaling Server) |  (WSS Encrypted)  |  (Controlled)  |
+----------------+                   +------------------+                   +----------------+
        |                                                                           |
        | <======================= DTLS Encrypted P2P Tunnel ======================> |
        |                 (Video, Audio, Keyboard, Mouse, Clipboard)                |
        |                       * E2EE Achieved Here *                          |
        +---------------------------------------------------------------------------+
```

> 实现结论： 通过TLS保护信令链路 + DTLS保护数据链路的分层策略，我们确保了用户数据在传输过程中的绝对隐私和安全。没有任何一方（包括我们自己）能够访问或解密用户在远程会话期间传输的任何实质性内容。这不仅是一项技术要求，更是我们对用户隐私承诺的基石。

# 信令服务器工作流程与API协议

---

### 1. 核心目标与设计哲学

本设计文档旨在为信令服务器（Signaling Server）提供一份详尽的开发蓝图。信令服务器是整个远程桌面系统的 “连接协调中心”，其核心职责是 身份认证、设备配对 与 引导P2P连接建立。

设计遵循以下原则：
- 轻量级与专注： 服务器只处理信令（Signaling），绝不触碰任何媒体数据流（Media Stream），确保低延迟、高可扩展性及用户隐私。
- 状态明确： 客户端与会话状态将被精确管理，确保通信流程的健壮性。
- 协议无歧义： 所有API消息结构清晰，字段定义明确，杜绝模糊地带。

---

### 2. 整体工作流程

系统通过一个明确分阶段的流程，引导两个客户端（控制端和被控端）从独立的个体转变为建立加密P2P连接的会话伙伴。



流程概述:
1.  注册与待命: 被控端启动后，连接信令服务器并使用其唯一的设备ID（`deviceID`）登录，进入等待连接状态。
2.  发起连接: 控制端向信令服务器发起配对请求，提供目标被控端的`deviceID`和一次性密码（OTP）。
3.  请求转发与确认: 服务器验证请求后，向被控端转发会话请求。被控端确认后，会话正式建立。
4.  WebRTC协商 (ICE): 服务器在两个客户端之间充当透明代理，转发`SDP Offer`、`SDP Answer`及`ICE Candidate`消息，直至双方成功建立P2P连接。
5.  会话结束: 任意一方断开连接或主动结束会话，服务器将通知对端并清理会话资源。

---

### 3. 详细工作阶段剖析

#### 3.1 阶段一：连接与认证流程

此阶段的目标是让客户端在信令服务器上完成身份注册，进入可被发现的状态。

1.  WebSocket连接: 客户端（控制端或被控端）通过安全WebSocket (`wss://`) 连接到信令服务器。每个连接在服务器端都拥有一个唯一的 `connectionID`。
2.  发送认证请求: 连接建立后，客户端必须发送的第一条消息是 `login`。
    - 被控端: 提供其持久化的 `deviceID`。
    - 控制端: 也提供其 `deviceID`，用于身份标识。
3.  服务器处理:
    - 服务器收到 `login` 消息后，维护一个在线设备映射表： `map[deviceID] -> connectionID`。
    - 如果 `deviceID` 已存在，则更新其 `connectionID`（允许同一设备重新连接）。
    - 认证成功后，服务器向客户端回送 `login-success` 消息。
    - 若认证失败（例如，`deviceID`格式无效），则回送 `login-failed` 并关闭连接。

#### 3.2 阶段二：设备配对与会话发起

此阶段由控制端发起，旨在与一个特定的被控端建立一个逻辑会话。

1.  控制端发起配对: 控制端向服务器发送 `pair-request` 消息，负载中包含目标被控端的`targetDeviceID`和用于验证的一次性密码 `otp`。
2.  服务器处理配对请求:
    - 服务器检查 `targetDeviceID` 是否存在于在线设备映射表中。
    - 若目标不存在或离线: 向控制端回复 `pair-response` 消息，状态为 `peer_not_found`。
    - 若目标在线: 服务器不直接验证OTP。它将 `pair-request` 的内容（包括发起方的`deviceID`和`otp`）封装进一条 `session-request` 消息，并转发给目标被控端。
3.  被控端处理会话请求:
    - 被控端收到 `session-request` 后，在本地验证 `otp` 是否有效。
    - 若验证通过: 向服务器发送 `session-accept` 消息。服务器此时正式创建一个会话实例，记录双方的`connectionID`，并将双方状态标记为 `in-session`。随后，服务器向双方同时发送 `session-ready` 消息，通知它们可以开始WebRTC协商。
    - 若验证失败: 向服务器发送 `session-reject` 消息。服务器将此结果通过 `pair-response`（状态为 `rejected_by_peer`）转发给控制端，并清理此次失败的配对尝试。

#### 3.3 阶段三：ICE信令交换协议 (核心)

会话双方收到 `session-ready` 后，立即开始标准的WebRTC NAT穿透流程。在此阶段，信令服务器的角色是纯粹的消息中继。

交互顺序:
1.  Offer生成与发送:
    - 控制端作为发起方，创建 `RTCPeerConnection`，生成`SDP Offer`。
    - 控制端将此`Offer`封装在`offer`消息中发送给信令服务器。
2.  Offer转发:
    - 服务器收到 `offer` 消息后，根据会话信息找到被控端的`connectionID`，并将 `offer` 消息原封不动地转发过去。
3.  Answer生成与发送:
    - 被控端收到 `offer` 消息，将其设置到自己的`RTCPeerConnection`中，并生成`SDP Answer`。
    - 被控端将此`Answer`封装在`answer`消息中发送给信令服务器。
4.  Answer转发:
    - 服务器收到 `answer` 消息后，将其转发给控制端。
5.  ICE Candidate交换 (双向并行):
    - 在上述`Offer`/`Answer`交换的同时，双方的WebRTC堆栈会不断发现本地网络候选地址（ICE Candidate）。
    - 每当任何一方发现一个新的`ICE Candidate`，就将其封装在`ice-candidate`消息中发送给信令服务器。
    - 服务器收到 `ice-candidate` 消息后，立即将其转发给会话中的另一方。
    - 这个交换过程会持续进行，直到双方的WebRTC堆栈成功建立P2P连接或确定需要通过TURN中继。

> 关键设计: `offer`, `answer`, `ice-candidate` 消息本身不包含目标地址。服务器根据消息来源的 `connectionID`，自动路由到会话中的另一方。这简化了客户端逻辑并增强了服务器的控制力。

---

### 4. API协议定义

所有通信均通过WebSocket以JSON格式进行。

| 消息类型 (type) | 方向 | 负载 (payload) 结构 | 字段描述 |
| :--- | :--- | :--- | :--- |
| `login` | C → S | ```json{ "deviceID": "string" }``` | deviceID: 客户端的唯一设备标识符。 |
| `login-success` | S → C | ```json{ "message": "string" }``` | message: 成功登录的确认信息。 |
| `login-failed` | S → C | ```json{ "reason": "string" }``` | reason: 登录失败的原因。 |
| `pair-request` | C → S | ```json{ "targetDeviceID": "string", "otp": "string" }``` | targetDeviceID: 目标被控端的设备ID。<br>otp: 用于验证的一次性密码。 |
| `pair-response` | S → C | ```json{ "status": "string", "message": "string" }``` | status: `peer_not_found`, `rejected_by_peer`, `pairing_initiated`等。<br>message: 描述性信息。 |
| `session-request` | S → C | ```json{ "fromDeviceID": "string", "otp": "string" }``` | fromDeviceID: 发起配对请求的控制端ID。<br>otp: 由控制端提供的OTP，供被控端验证。 |
| `session-accept` | C → S | `{}` | 无负载。表示被控端接受会话请求。 |
| `session-reject` | C → S | `{}` | 无负载。表示被控端拒绝会话请求。 |
| `session-ready` | S → C | ```json{ "sessionID": "string", "peerDeviceID": "string" }``` | sessionID: 服务器为此会话生成的唯一ID。<br>peerDeviceID: 会话对端的设备ID。 |
| `offer` | C ↔ S ↔ C | ```json{ "sdp": { "type": "offer", "sdp": "string" } }``` | sdp: WebRTC的SDP Offer对象。 |
| `answer` | C ↔ S ↔ C | ```json{ "sdp": { "type": "answer", "sdp": "string" } }``` | sdp: WebRTC的SDP Answer对象。 |
| `ice-candidate` | C ↔ S ↔ C | ```json{ "candidate": { "candidate": "string", "sdpMid": "string", "sdpMLineIndex": "number" } }``` | candidate: WebRTC的ICE Candidate对象。 |
| `end-session` | C → S | `{}` | 无负载。客户端主动请求结束当前会话。 |
| `session-ended` | S → C | ```json{ "reason": "string" }``` | reason: 会话结束的原因，如 `user_terminated`, `peer_disconnected`。 |
| `error` | S → C | ```json{ "message": "string" }``` | message: 服务器向客户端发送的通用错误信息。 |

通用消息结构:
所有消息都应遵循以下顶层结构，以便于解析和路由。
```json
{
  "type": "string",  // 消息类型，必须，例如 "login", "offer"
  "payload": {}      // 消息负载，可选，结构如上表定义
}
```

---

### 5. 会话管理机制

服务器必须能够稳健地管理客户端连接和会话生命周期。

#### 5.1 状态管理

服务器需要维护两个核心的状态映射：
- 在线客户端 (`OnlineClients`): 一个从 `deviceID` 到 `connectionID` 及客户端状态（`idle`, `in-session`）的映射。
  - `map[deviceID]ClientState{ connectionID, status }`
- 活动会话 (`ActiveSessions`): 一个从 `sessionID` 到会话参与者信息的映射。
  - `map[sessionID]Session{ controllingID, controlledID }`

#### 5.2 意外断线处理

- 心跳检测: 服务器应实现WebSocket的`Ping`/`Pong`机制。若在指定时间内未收到`Pong`响应，则认为连接已断开。
- 处理流程:
    1.  当检测到某个`connectionID`断开连接时，服务器首先从`OnlineClients`映射中移除该客户端。
    2.  检查该客户端的状态是否为 `in-session`。
    3.  如果是，则查找其所在的会话(`ActiveSessions`)。
    4.  向会话中的另一方（对端）发送 `session-ended` 消息，`reason` 设置为 `peer_disconnected`。
    5.  从 `ActiveSessions` 中删除该会话记录，并将对端的状态重置为 `idle`。

#### 5.3 优雅会话终结

1.  客户端发起: 任意一方客户端可以发送 `end-session` 消息来主动结束会话。
2.  服务器处理:
    - 服务器收到 `end-session` 后，找到该客户端所在的会话。
    - 向会话中的另一方发送 `session-ended` 消息，`reason` 设置为 `user_terminated`。
    - 从 `ActiveSessions` 中删除该会话。
    - 将双方客户端的状态都重置为 `idle`，使它们可以发起或接收新的会话请求。

该机制确保了资源的正确释放，并为用户提供了清晰的会话结束反馈。

# 控制端（Controlling Client）软件实现计划

---

### 引言与目标

本计划为“控制端”软件模块提供一份详尽的技术实现指南。作为用户直接交互的界面，控制端的首要目标是实现 “零感延迟” 的画面渲染和 “精准同步” 的输入控制。本文档将详细阐述远程画面渲染、用户输入捕获及通信协议接口三大核心模块的实现方案，确保与“被控端”软件无缝对接，共同构成一个高性能、响应迅速的远程桌面系统。

---

### 1. 远程画面渲染方案：Tauri Webview 驱动的高性能渲染引擎

核心目标： 在 Tauri 的 Webview 环境中，利用现代浏览器技术栈，以最低的CPU占用和延迟，解码并渲染来自被控端的视频流。

#### 1.1. 核心渲染路径：WebCodecs 硬件加速解码

选定技术： `WebCodecs API`

论证： `WebCodecs` 是一个现代浏览器API，它提供了对浏览器内置媒体编解码器的底层访问能力，包括硬件加速的视频解码器。通过它，我们可以将解码任务完全移交至 GPU，极大地释放 CPU 资源，是实现低延迟、高帧率渲染的最优选择。

实现流程：

1.  数据接收： Rust 后端通过 WebRTC 连接接收到加密的 H.264/HEVC 视频数据块（`EncodedVideoChunk`）。
2.  数据转发： Rust 后端通过 Tauri 的 IPC 通道（`window.emit`）将视频数据块高效地发送到前端 JavaScript 环境。
3.  解码器配置与实例化：
    *   在 JavaScript 中，首先检测浏览器是否支持 `VideoDecoder` (`if ('VideoDecoder' in window)`）。
    *   根据视频流的元信息（如H.264的SPS/PPS），配置并创建一个 `VideoDecoder` 实例。
    ```javascript
    const videoDecoder = new VideoDecoder({
      output: handleDecodedFrame,
      error: (e) => console.error(e),
    });
    
    videoDecoder.configure({
      codec: "avc1.42E01E", // Example H.264 configuration
      // hardwareAcceleration: "prefer-hardware" // 可选配置
    });
    ```
4.  解码与渲染循环：
    *   每当从 Rust 接收到新的视频数据块时，将其送入解码器：`videoDecoder.decode(encodedChunk)`。
    *   `VideoDecoder` 在 GPU 中完成解码后，会通过 `output` 回调函数返回一个 `VideoFrame` 对象。此对象是对 GPU 显存中已解码图像的引用，零拷贝。
    *   在 `requestAnimationFrame` 回调中，将 `VideoFrame` 直接绘制到 `<canvas>` 上，实现流畅渲染：`canvasContext.drawImage(videoFrame, 0, 0)`。
    *   使用完毕后，必须关闭 `VideoFrame` (`videoFrame.close()`) 以释放底层内存。

#### 1.2. 备用渲染路径：基于 WebAssembly 的软件解码

触发条件： 当浏览器环境不支持 `WebCodecs API` 时，系统必须自动降级到软件解码方案，以保证兼容性。

选定技术： 使用高性能的 WebAssembly (WASM) H.264 解码库（例如 `libav.js` 或类似的 `openh264` 封装）。

实现流程：

1.  数据流： 数据接收和转发流程与硬件加速路径相同。
2.  软件解码： JavaScript 将接收到的视频数据块传递给 WASM 解码器实例。
3.  数据转换与渲染：
    *   WASM 解码器在 CPU 上完成解码，通常输出为 YUV 或 RGBA 格式的原始像素数据（`Uint8Array`）。
    *   如果输出是 YUV，需要先在 JavaScript 或通过另一个 WASM 模块将其转换为 RGBA。
    *   使用 `canvasContext.createImageData()` 创建一个 `ImageData` 对象，并将解码后的 RGBA 数据填充进去。
    *   最后调用 `canvasContext.putImageData(imageData, 0, 0)` 将图像渲染到画布上。

性能考量： 软件解码会显著增加 CPU 占用率，并引入额外的内存拷贝开销。在高分辨率或高帧率下，可能会成为性能瓶颈。因此，应在UI中明确告知用户当前正处于软件解码模式，并建议其使用支持硬件解码的现代浏览器。

---

### 2. 用户输入捕获与处理：跨平台的精准输入捕accomodate与标准化

核心目标： 在 WebView 中精确捕获用户的全部输入意图，将其标准化为统一格式，并以最低延迟发送至被控端。

#### 2.1. 事件捕获层设计

输入捕获的焦点是作为远程画面载体的 `<canvas>` 元素。

*   事件监听器： 为 `<canvas>` 元素全面注册以下 DOM 事件监听器：
    *   鼠标事件: `mousemove`, `mousedown`, `mouseup`, `wheel`
    *   键盘事件: `keydown`, `keyup`
    *   上下文菜单: `contextmenu` (用于捕获右键点击并阻止浏览器默认菜单)
*   焦点管理： 确保用户点击 `<canvas>` 后，其能立即获得键盘输入焦点。
*   浏览器默认行为抑制： 在事件处理器中，适当调用 `event.preventDefault()` 来阻止浏览器执行默认操作，如页面滚动、右键菜单、拖拽选择等。

#### 2.2. 事件标准化与坐标转换

为了让被控端能理解并精确复现操作，所有捕获的原生事件都必须转换为一种标准的、与平台无关的格式。

*   鼠标坐标：
    *   捕获 `event.offsetX` 和 `event.offsetY` 作为画布内的本地坐标。
    *   必须进行坐标转换，以应对控制端窗口缩放和分辨率不匹配的情况。
    *   转换公式：
        `remoteX = event.offsetX * (remoteScreenWidth / canvas.clientWidth)`
        `remoteY = event.offsetY * (remoteScreenHeight / canvas.clientHeight)`
*   键盘按键：
    *   关键： 使用 `event.code` 属性（如 `"KeyW"`, `"ArrowUp"`, `"ControlLeft"`）。它代表物理按键位置，不受操作系统键盘布局影响，是远程控制场景下的唯一可靠标识。
    *   禁止使用 `event.key` (`"w"`, `"W"`, `"ArrowUp"`)，因为它会随布局和修饰键变化。
    *   同时记录 `ctrlKey`, `altKey`, `shiftKey`, `metaKey` 等修饰键的状态。
*   鼠标滚轮：
    *   捕获 `event.deltaX` 和 `event.deltaY`。需要进行归一化处理，因为不同浏览器和设备对滚轮增量的定义可能不同。

#### 2.3. 数据流与序列化格式

流程： `前端JS事件` -> `标准化JS对象` -> `Tauri IPC调用` -> `Rust后端结构体` -> `序列化` -> `WebRTC发送`

序列化格式：

*   开发阶段 (推荐): JSON
    *   优势: 人类可读，调试方便，JavaScript 原生支持，Rust 有强大的 `serde_json` 库。
    *   示例:
    ```json
    // 鼠标移动
    {
      "type": "MouseMove",
      "x": 1920.0,
      "y": 1080.0
    }
    
    // 键盘按下
    {
      "type": "KeyDown",
      "code": "KeyA",
      "modifiers": { "shift": true, "ctrl": false, "alt": false, "meta": false }
    }
    
    // 鼠标点击
    {
      "type": "MouseDown",
      "button": "left" // "left", "right", "middle"
    }
    ```

*   生产阶段 (可选优化): Protobuf 或 Bincode
    *   优势: 二进制格式，体积更小，序列化/反序列化速度更快，能有效降低网络负载和处理延迟。
    *   考量: 需要维护 `.proto` 文件定义，调试相对复杂。此优化可在核心功能稳定后引入。

---

### 3. 通信协议接口实现：基于 WebRTC DataChannel 的差分通信

核心目标： 利用 WebRTC DataChannel 的不同配置，为不同类型的控制数据建立最优的传输通道，平衡延迟与可靠性。

#### 3.1. WebRTC 连接建立

控制端与被控端的连接建立过程完全对称。它将通过安全的 WebSocket 连接与信令服务器通信，交换 SDP 和 ICE 候选地址，以建立端到端的加密 P2P 连接。

#### 3.2. 数据通道（DataChannel）差分策略

为实现极致的输入响应，我们不应将所有数据都塞入一个通道，而是根据数据特性建立多个具有不同服务质量（QoS）的通道。

| 通道名称 | 传输内容 | 配置 (`RTCDataChannelInit`) | 核心 rationale (设计原因) |
| :--- | :--- | :--- | :--- |
| `input-critical` | 键盘按键 (`KeyDown`/`KeyUp`)<br>鼠标点击 (`MouseDown`/`MouseUp`)<br>剪贴板数据 | `ordered: true`<br>`maxRetransmits: null` (可靠) | 绝不容忍丢失。 丢失一个按键弹起或鼠标点击事件会造成灾难性后果（如按键卡住）。必须保证按序、可靠送达。 |
| `input-realtime` | 鼠标移动 (`MouseMove`)<br>鼠标滚轮 (`Wheel`) | `ordered: true`<br>`maxRetransmits: 0` (不可靠) | 延迟是第一要务。 旧的鼠标位置信息毫无价值。允许在网络拥堵时丢弃数据包，以确保用户看到的是最新的光标位置。`ordered: true` 可防止光标路径混乱。 |
| `control-feedback` | 发送控制命令（如请求I帧、调整画质）<br>接收被控端的反馈（如性能统计、丢包率报告） | `ordered: true`<br>`maxRetransmits: null` (可靠) | 保证信令完整性。 控制信令和重要的性能反馈必须可靠传输，以确保双方状态同步和动态码率调整算法的正常工作。 |

实现要点：
在 WebRTC 连接建立后，控制端和被控端需要按照此约定，使用相同的标签（如 `"input-critical"`) 和配置创建这些 DataChannel。

结论

本控制端实现计划通过采用 硬件加速渲染、精准输入捕获 和 差分数据通道 三大核心策略，确保了与被控端设计的高度协同。此方案不仅在技术上追求极致性能，更在架构层面充分考虑了用户体验的每一个细节，为打造一款流畅、精准、响应迅速的下一代远程桌面软件奠定了坚实的基础。

# 被控端（Controlled Client）软件实现计划

---

### 引言与目标

本计划为“被控端”软件模块提供一份详尽、可执行的技术实现指南。基于已确定的 Rust + Tauri 客户端架构及前期技术研究，本文档将深入阐述屏幕与音频捕获、数据编码、输入注入及网络通信四大核心领域的实现策略与具体步骤。此计划旨在作为开发团队的直接技术蓝图，确保实现一个高性能、低延迟且安全可靠的被控端。

---

### 1. 屏幕与音频捕获方案

核心目标： 实现跨平台、高效、低延迟的媒体流捕获，并确保音视频同步。

#### 1.1. Windows 平台屏幕捕获

选定技术： `DXGI Desktop Duplication API`

论证： 此 API 为 Windows 8 及以上系统提供了最高效的屏幕捕获机制。它直接利用 GPU 进行操作，CPU 占用极低，性能远超传统的 GDI `BitBlt` 方法。这是实现低延迟远程桌面的首选方案。

实现步骤：
1.  设备与适配器枚举：
    *   使用 `CreateDXGIFactory` 创建 `IDXGIFactory` 实例。
    *   通过 `EnumAdapters` 遍历 GPU 适配器 (`IDXGIAdapter`)。
    *   通过 `EnumOutputs` 遍历连接到适配器的显示器 (`IDXGIOutput`)。

2.  创建复制接口：
    *   从目标 `IDXGIOutput` 查询 `IDXGIOutput1` 接口。
    *   调用 `IDXGIOutput1::DuplicateOutput()` 方法创建核心的 `IDXGIOutputDuplication` 接口实例。此实例代表了一个可用于捕获桌面图像的会话。

3.  捕获循环（Frame Acquisition Loop）：
    *   在一个独立的捕获线程中，循环调用 `IDXGIOutputDuplication::AcquireNextFrame()`。
        *   该函数以极低的延迟等待下一帧的到来。可设置一个超时时间。
        *   成功时，返回一个包含桌面图像的 `IDXGIResource`。
    *   处理帧数据： 获取到的 `IDXGIResource` 必须映射到 CPU 可访问的内存中。
        *   创建一个 `D3D11_TEXTURE2D_DESC` 描述，将 `Usage` 设为 `D3D11_USAGE_STAGING`，`CPUAccessFlags` 设为 `D3D11_CPU_ACCESS_READ`。
        *   使用此描述创建一张临时的 Staging Texture。
        *   调用 `ID3D11DeviceContext::CopyResource()` 将 GPU 上的帧数据复制到 Staging Texture。
        *   调用 `ID3D11DeviceContext::Map()` 映射 Staging Texture，获取指向 `BGRA` 格式像素数据的指针。
    *   脏矩形处理（可选但推荐）：
        *   调用 `IDXGIOutputDuplication::GetFrameDirtyRects()` 获取自上一帧以来发生变化的区域列表。
        *   编码时仅对这些“脏矩形”区域进行处理和发送，可大幅降低编码负担和网络带宽。*注意：在某些Windows版本中，此API可能返回整个屏幕，需要实现前后帧对比作为备用方案。*
    *   释放帧： 处理完毕后，必须调用 `IDXGIOutputDuplication::ReleaseFrame()` 释放资源，以便 GPU 捕获下一帧。

#### 1.2. macOS 平台屏幕捕获

选定技术： `ScreenCaptureKit` (macOS 13+) 作为首选，`AVFoundation` 作为备用。

论证： `ScreenCaptureKit` 是苹果在 WWDC22 推出的现代化框架，专为高性能屏幕共享设计。它提供更精细的控制（窗口、应用、显示器）、更高的效率和更低的系统开销，并原生处理隐私权限。

实现步骤 (`ScreenCaptureKit`):
1.  权限请求： 应用必须在 `Info.plist` 中声明屏幕录制权限，并在首次使用时向用户请求授权。
2.  获取可捕获内容：
    *   调用 `SCShareableContent.getShareableContent()` 获取当前可用的窗口、应用和显示器列表。
    *   在 UI 中向用户展示此列表，让其选择要共享的内容。
3.  配置流：
    *   根据用户选择创建一个 `SCContentFilter` 实例。
    *   创建一个 `SCStreamConfiguration`，配置输出参数，如像素格式 (`kCVPixelFormatType_32BGRA`)、宽度、高度、最小帧率等。
    *   强烈建议启用 `capturesAudio` 选项以同时捕获系统音频，这能确保音视频的完美同步。
4.  创建并启动流：
    *   使用上述配置和过滤器创建 `SCStream` 实例。
    *   设置一个实现了 `SCStreamOutput` 协议的代理对象，以接收视频帧 (`CMSampleBuffer`) 和音频数据。
    *   调用 `stream.startCapture()` 开始捕获。视频帧和音频样本将通过代理方法异步传递。

#### 1.3. 音频捕获与同步

核心挑战： 捕获系统输出的音频，并将其与对应的视频帧对齐。

*   Windows:
    *   技术: 使用 WASAPI (Windows Audio Session API) 的 Loopback 模式。
    *   实现:
        1.  初始化 COM 库。
        2.  使用 `IMMDeviceEnumerator` 获取默认的音频渲染端点 (`eRender`)。
        3.  激活该端点的 `IAudioClient` 接口。
        4.  以 Loopback 模式初始化 `IAudioClient`。
        5.  获取 `IAudioCaptureClient` 接口，并从其缓冲区中循环读取系统输出的音频数据。

*   macOS:
    *   技术: 如上所述，`ScreenCaptureKit` 提供了内置的音频捕获能力，这是最简单且最可靠的方式。
    *   备用方案: 若不使用 `ScreenCaptureKit`，则需要安装一个虚拟音频设备（如开源的 `BlackHole`），并将系统输出路由到此设备，然后应用从该虚拟设备捕获音频。此方案复杂且用户体验不佳。

*   音视频同步:
    *   捕获到的每一帧视频和每一个音频样本都应附带一个高精度的时间戳（Presentation Timestamp, PTS）。
    *   `ScreenCaptureKit` 和 `DXGI` 都提供帧时间戳信息。WASAPI 也可获取样本时间戳。
    *   在编码和封装阶段，将这些时间戳传递给编码器和复用器（Muxer）。
    *   接收端根据这些时间戳进行同步播放。

---

### 2. 数据处理与编码策略

核心目标： 在保证画面质量的同时，最大限度降低CPU占用，并能动态适应网络变化。

#### 2.1. 视频编码与硬件加速

编码器选型： 优先使用 H.264，因其硬件支持最广泛，兼容性最好。可考虑 H.265 (HEVC) 作为高质量选项，但需注意硬件支持情况。

硬件加速策略： 实现一个抽象的编码器层，动态检测并优先使用平台提供的硬件编码API。

| 平台 | 硬件加速API | Rust Crate (示例) | 后备方案 (Software) |
| :--- | :--- | :--- | :--- |
| Windows (NVIDIA) | NVIDIA NVENC | `nvenc-rs` | `openh264` |
| Windows (Intel) | Intel Quick Sync Video (QSV) | (需封装 `libmfx` 或使用FFmpeg绑定) | `openh264` |
| Windows (AMD) | AMD AMF/VCN | (需封装AMF SDK或使用FFmpeg绑定) | `openh264` |
| macOS / iOS | VideoToolbox | `video-toolbox` | `openh264` |

实现逻辑：
1.  应用启动时，探测系统中可用的硬件编码器。
2.  根据用户配置或默认优先级选择编码器。
3.  如果硬件编码器初始化失败，自动回退到 `openh264` 软件编码。

#### 2.2. 动态码率调整算法框架

设计思路： 基于 WebRTC GCC (Google Congestion Control) 的思想，实现一个由接收端反馈驱动的码率控制器（运行在被控端）。

1. 反馈信息收集：
   *   控制端（Peer）需定期通过 WebRTC DataChannel 发回 RTCP Receiver Report 或自定义的反馈消息。
   *   关键指标包括：
     *   往返时间 (RTT): 衡量网络延迟。
     *   丢包率 (Packet Loss Fraction): 衡量网络拥塞程度。
     *   接收端估算的可用带宽 (REMB): (如果可用)

2. 控制器逻辑（基于AIMD - 加性增，乘性减）：
   *   状态机： 维护当前网络状态：*Under-use* (欠载), *Normal* (正常), *Over-use* (过载)。
   *   延迟梯度分析：
     *   通过分析连续RTT的变化趋势来判断延迟是增加、减少还是稳定，以此来预测网络拥塞。
     *   如果延迟持续稳定或下降： 状态为 `Normal` 或 `Under-use`。
     *   如果延迟出现突增或持续上升： 状态切换为 `Over-use`。
   *   码率调整规则：
     *   `Over-use` 状态或丢包率 > 10%：
       *   乘性降低码率。 `target_bitrate = current_bitrate * 0.85`。快速响应以缓解拥塞。
     *   `Normal` 状态且丢包率 < 2%：
       *   加性增加码率。 `target_bitrate = current_bitrate + small_increment`。缓慢探测更高带宽。
     *   丢包率在 2% - 10% 之间：
       *   保持当前码率不变。 网络处于临界状态，避免进一步恶化。

3. 执行：
   *   码率控制器计算出的 `target_bitrate` 将被实时应用于视频编码器实例，调整其输出码率。

---

### 3. 输入事件注入机制

核心目标： 安全、精确地模拟来自控制端的键盘和鼠标操作。

#### 3.1. 安全接收

所有输入事件必须通过已建立的、经 DTLS 加密 的 WebRTC DataChannel 接收。这从根本上保证了传输过程的机密性和完整性，防止中间人攻击。

#### 3.2. Windows 平台输入注入

选定技术： `SendInput` API

论证： 这是现代 Windows 系统中模拟用户输入的标准、可靠方法，比旧的 `mouse_event` 和 `keybd_event` 更强大，能减少被某些应用程序或游戏屏蔽的概率。

实现步骤：
1.  接收到来自控制端的输入事件（如 "鼠标移动 {x, y}" 或 "键盘按下 {key_code}"）。
2.  根据事件类型，构造一个 `INPUT` 结构体数组。
    *   鼠标事件: 设置 `type` 为 `INPUT_MOUSE`，并填充 `mi` 结构体（`dx`, `dy`, `mouseData`, `dwFlags` 等）。
    *   键盘事件: 设置 `type` 为 `INPUT_KEYBOARD`，并填充 `ki` 结构体（`wVk`, `wScan`, `dwFlags` 等）。
3.  调用 `SendInput` 函数，将 `INPUT` 数组一次性注入系统事件队列。
```rust
// 伪代码示例
use winapi::um::winuser::{SendInput, INPUT, MOUSEINPUT, INPUT_MOUSE, MOUSEEVENTF_MOVE};

fn simulate_mouse_move(dx: i32, dy: i32) {
    let mut input = INPUT {
        type: INPUT_MOUSE,
        u: unsafe { std::mem::zeroed() },
    };
    let mut mouse_input = MOUSEINPUT {
        dx,
        dy,
        mouseData: 0,
        dwFlags: MOUSEEVENTF_MOVE,
        time: 0,
        dwExtraInfo: 0,
    };
    unsafe {
        *input.u.mi_mut() = mouse_input;
        SendInput(1, &mut input, std::mem::size_of::<INPUT>() as i32);
    }
}
```

#### 3.3. macOS 平台输入注入

选定技术： `Core Graphics` Event APIs

核心前提： 应用必须获得用户的 “辅助功能” 授权。这是 macOS 的一项核心安全机制。
*   实现： 首次尝试注入事件前，需检测权限。若无权限，应通过 UI 引导用户前往 `系统设置 -> 隐私与安全性 -> 辅助功能` 手动启用。

实现步骤：
1.  接收到输入事件。
2.  根据事件类型创建 `CGEvent`：
    *   鼠标事件: 使用 `CGEventCreateMouseEvent()` 创建事件，指定事件类型（移动、点击、拖拽）、位置和按钮状态。
    *   键盘事件: 使用 `CGEventCreateKeyboardEvent()` 创建事件，指定虚拟键码和按键状态（按下/弹起）。
3.  使用 `CGEventPost()` 将创建的事件发布到系统事件流中。
```rust
// 伪代码示例，需使用 `core-graphics` crate
use core_graphics::event::{CGEvent, CGEventType, CGMouseButton, CGEventTapLocation};
use core_graphics::geometry::CGPoint;

fn simulate_mouse_click(pos: CGPoint) {
    let down = CGEvent::new_mouse_event(
        CGEventSource::null(),
        CGEventType::LeftMouseDown,
        pos,
        CGMouseButton::Left,
    ).unwrap();
    let up = CGEvent::new_mouse_event(
        CGEventSource::null(),
        CGEventType::LeftMouseUp,
        pos,
        CGMouseButton::Left,
    ).unwrap();
    
    down.post(CGEventTapLocation::HID);
    up.post(CGEventTapLocation::HID);
}
```

---

### 4. 网络通信接口

核心目标： 定义清晰的通信协议，并利用 WebRTC 实现安全、高效的数据传输。

#### 4.1. 接口定义

被控端需要与两个实体通信：

1.  信令服务器 (Signaling Server):
    *   协议: WebSocket (WSS)。
    *   职责:
        *   身份认证与注册（使用设备ID）。
        *   交换 SDP (Session Description Protocol) `offer`/`answer` 以协商会话。
        *   交换 ICE (Interactive Connectivity Establishment) 候选地址，用于 NAT 穿透。
    *   生命周期: 连接仅在会话建立阶段活跃。

2.  对等端 (Peer - 控制端):
    *   协议: WebRTC (基于 SRTP 和 SCTP)。
    *   职责:
        *   媒体流传输: 通过 SRTP (Secure Real-time Transport Protocol) 发送加密的 H.264 视频流和音频流。
        *   数据通道传输: 通过基于 DTLS 加密的 SCTP (Stream Control Transmission Protocol) 数据通道（DataChannel）进行双向通信。
            *   发送: 遥测数据（性能指标、日志等）。
            *   接收: 输入事件、控制命令、剪贴板数据。

#### 4.2. 数据封装与传输

工作流程：
1.  编码后的媒体帧： 经过硬件或软件编码器处理后，H.264 视频帧和音频数据包被封装成 RTP 包。
2.  安全传输： 这些 RTP 包通过 WebRTC 的媒体通道（`MediaChannel`）发送，该通道使用 SRTP 进行端到端加密。
3.  控制数据封装：
    *   输入事件、剪贴板内容等控制数据被序列化（如 JSON 或 Protobuf）。
    *   为不同类型的数据创建不同的数据通道，并配置其属性：
        *   键盘事件/剪贴板: 需要 可靠且有序 的通道 (`ordered: true`, `maxRetransmits: None`)。
        *   鼠标移动事件: 可使用 不可靠但有序 的通道 (`ordered: true`, `maxRetransmits: 0`)，以在弱网下优先保证低延迟，丢弃旧的位置信息。
4.  安全数据通道： 序列化后的数据通过配置好的 WebRTC DataChannel 发送。WebRTC 栈会自动处理其在 SCTP 上的封装和在 DTLS 上的加密。

结论：
本实现计划为被控端模块的开发提供了全面的技术路径。通过优先采用各平台的现代化高性能API，并结合 WebRTC 的强大功能，我们能够构建一个响应迅速、资源占用低且通信安全的远程桌面客户端，为最终用户提供卓越体验。开发团队应严格遵循此计划，并针对具体实现中的细节问题进行持续优化。