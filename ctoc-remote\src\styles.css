:root {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
  color: #333;
  background-color: #f5f5f5;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: 100vh;
  overflow: hidden;
}

#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.device-info {
  display: flex;
  gap: 1rem;
  align-items: center;
  font-size: 0.9rem;
}

.status-disconnected {
  color: #ff6b6b;
}

.status-connected {
  color: #51cf66;
}

.status-connecting {
  color: #ffd43b;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 1rem;
  padding: 1rem;
  overflow: hidden;
}

.panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 1.5rem;
  overflow-y: auto;
}

#connection-panel {
  width: 350px;
  flex-shrink: 0;
}

#desktop-panel {
  flex: 1;
  padding: 1rem;
}

#status-panel {
  width: 300px;
  flex-shrink: 0;
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  color: #495057;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #495057;
}

.form-group input {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.form-group button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.form-group button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.form-group button:disabled {
  background: #adb5bd;
  cursor: not-allowed;
}

.otp-code {
  background: #f8f9fa;
  border: 2px dashed #667eea;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  margin-top: 1rem;
}

.code {
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  letter-spacing: 0.2em;
}

.timer {
  display: block;
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

.desktop-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.desktop-container {
  position: relative;
  width: 100%;
  height: calc(100% - 60px);
  background: #000;
  border-radius: 6px;
  overflow: hidden;
}

#remote-canvas {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: none;
}

.status-log {
  height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 0.5rem;
}

.log-info {
  color: #495057;
}

.log-success {
  color: #51cf66;
}

.log-warning {
  color: #ffd43b;
}

.log-error {
  color: #ff6b6b;
}

@media (prefers-color-scheme: dark) {
  :root {
    color: #e9ecef;
    background-color: #212529;
  }

  .panel {
    background: #343a40;
    color: #e9ecef;
  }

  .form-group input {
    background: #495057;
    border-color: #6c757d;
    color: #e9ecef;
  }

  .form-group input:focus {
    border-color: #667eea;
  }

  .section h3 {
    color: #e9ecef;
  }

  .form-group label {
    color: #e9ecef;
  }

  .otp-code {
    background: #495057;
    border-color: #667eea;
  }

  .desktop-controls {
    background: #495057;
  }

  .status-log {
    background: #495057;
  }
}
