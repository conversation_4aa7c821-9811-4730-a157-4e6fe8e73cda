[package]
name = "ctoc-remote"
version = "0.1.0"
description = "CtocSoft Remote Desktop Control Software"
authors = ["CtocSoft Team"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "ctoc_remote_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tokio = { version = "1", features = ["full"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
futures = "0.3"
async-trait = "0.1"
rand = "0.8"
base64 = "0.21"
sha2 = "0.10"
aes-gcm = "0.10"
webrtc = "0.11"
tungstenite = { version = "0.20", features = ["native-tls"] }
tokio-tungstenite = { version = "0.20", features = ["native-tls"] }
dirs = "5.0"

# Platform-specific dependencies
[target.'cfg(windows)'.dependencies]
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_Graphics_Gdi",
    "Win32_Graphics_Direct3D11",
    "Win32_Graphics_Dxgi",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_System_Com",
    "Win32_Media_Audio",
    "Win32_Media_Audio_Wasapi",
] }

[target.'cfg(target_os = "macos")'.dependencies]
core-graphics = "0.23"
core-foundation = "0.9"
cocoa = "0.25"
objc = "0.2"

