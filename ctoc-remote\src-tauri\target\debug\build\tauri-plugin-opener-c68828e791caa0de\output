cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=F:\test\ctoc-remote\src-tauri\target\debug\build\tauri-plugin-opener-c68828e791caa0de\out\tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=F:\test\ctoc-remote\src-tauri\target\debug\build\tauri-plugin-opener-c68828e791caa0de\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\mirrors.ustc.edu.cn-38d0e5eb5da2abae\tauri-plugin-opener-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
