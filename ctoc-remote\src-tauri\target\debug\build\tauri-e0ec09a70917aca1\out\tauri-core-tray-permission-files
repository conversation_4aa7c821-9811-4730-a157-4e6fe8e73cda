["\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\F:\\test\\ctoc-remote\\src-tauri\\target\\debug\\build\\tauri-e0ec09a70917aca1\\out\\permissions\\tray\\autogenerated\\default.toml"]