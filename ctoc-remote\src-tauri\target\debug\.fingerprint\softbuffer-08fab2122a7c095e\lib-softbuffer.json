{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 17786941303264299914, "deps": [[376837177317575824, "build_script_build", false, 15760464251588439576], [4143744114649553716, "raw_window_handle", false, 2349762867116203253], [5986029879202738730, "log", false, 13613251818465215128], [10281541584571964250, "windows_sys", false, 17674492645655225057]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-08fab2122a7c095e\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}