<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="/src/styles.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CtocSoft Remote Desktop</title>
    <script type="module" src="/src/main.ts" defer></script>
  </head>

  <body>
    <div id="app">
      <header class="header">
        <h1>CtocSoft Remote Desktop</h1>
        <div class="device-info">
          <span id="device-id">Device ID: Loading...</span>
          <span id="connection-status" class="status-disconnected">Disconnected</span>
        </div>
      </header>

      <main class="main-content">
        <!-- Connection Panel -->
        <div id="connection-panel" class="panel">
          <h2>Connection</h2>

          <div class="section">
            <h3>Server Configuration</h3>
            <div class="form-group">
              <label for="server-url">Signaling Server:</label>
              <input type="text" id="server-url" value="wss://localhost:8080" />
              <button id="connect-btn">Connect</button>
            </div>
          </div>

          <div class="section">
            <h3>Remote Control</h3>
            <div class="form-group">
              <label for="target-device">Target Device ID:</label>
              <input type="text" id="target-device" placeholder="Enter device ID to control" />
              <label for="otp-input">OTP Code:</label>
              <input type="text" id="otp-input" placeholder="Enter OTP code" maxlength="6" />
              <button id="control-btn" disabled>Start Control</button>
            </div>
          </div>

          <div class="section">
            <h3>Allow Remote Access</h3>
            <div class="form-group">
              <button id="generate-otp-btn">Generate OTP</button>
              <div id="otp-display" class="otp-code" style="display: none;">
                <span>OTP Code: </span>
                <span id="otp-code" class="code"></span>
                <span id="otp-timer" class="timer"></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Remote Desktop Panel -->
        <div id="desktop-panel" class="panel" style="display: none;">
          <div class="desktop-controls">
            <button id="disconnect-btn">Disconnect</button>
            <button id="fullscreen-btn">Fullscreen</button>
            <span id="quality-info">Quality: Auto</span>
          </div>
          <div class="desktop-container">
            <canvas id="remote-canvas" width="1920" height="1080"></canvas>
          </div>
        </div>

        <!-- Status Panel -->
        <div id="status-panel" class="panel">
          <h3>Status</h3>
          <div id="status-log" class="status-log"></div>
        </div>
      </main>
    </div>
  </body>
</html>
